#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
时间序列图模块

实现基于PyQtGraph的时间序列图，支持日期时间轴、实时数据流更新等功能。
"""

import numpy as np
import datetime
import time
from typing import Dict, List, Optional, Tuple, Union, Any

try:
    import pyqtgraph as pg
    from pyqtgraph.Qt import QtCore, QtGui
except ImportError:
    # 如果导入失败，可能会在运行时另行处理
    pg = None
    QtCore = None
    QtGui = None

from ..base_chart import BaseChart, ChartType
from ...utils.logger import logger


if pg is not None:
    class TimeAxisItem(pg.AxisItem):
        """自定义时间轴项目，用于格式化时间轴标签"""

        def __init__(self, orientation, format='%H:%M:%S', *args, **kwargs):
            """
            初始化时间轴

            Args:
                orientation: 轴方向
                format: 时间格式化字符串
            """
            super().__init__(orientation, *args, **kwargs)
            self.format = format

        def tickStrings(self, values, scale, spacing):
            """将时间戳转换为格式化的时间字符串"""
            result = []
            for value in values:
                try:
                    timestamp = value
                    dt = datetime.datetime.fromtimestamp(timestamp)
                    result.append(dt.strftime(self.format))
                except (ValueError, OSError):
                    # 如果转换失败，返回原始数值
                    result.append(f"{value:.1f}")
            return result

        def set_format(self, format_str):
            """设置时间格式"""
            self.format = format_str
else:
    # 如果pg不可用，创建一个占位符类
    class TimeAxisItem:
        def __init__(self, *args, **kwargs):
            pass


class TimeSeriesChart(BaseChart):
    """
    时间序列图类
    
    实现时间序列图的创建、数据管理和样式设置等功能。
    """
    
    def __init__(
        self, 
        title: str = "时间序列图", 
        x_label: str = "时间", 
        y_label: str = "数值",
        time_format: str = "%H:%M:%S"
    ):
        """
        初始化时间序列图
        
        Args:
            title: 图表标题
            x_label: X轴标签
            y_label: Y轴标签
            time_format: 时间格式化字符串
        """
        # 不直接调用父类初始化，因为需要替换X轴
        self.title = title
        self.x_label = x_label
        self.y_label = y_label
        self.chart_type = ChartType.TIME_SERIES
        self.time_format = time_format
        
        # 图表布局和数据相关
        self._plot_widget = None
        self._plot_item = None
        self._legend = None
        self._grid_enabled = True
        self._data_items = {}  # 存储所有数据项目（如曲线、散点等）
        
        # 样式相关
        self._background_color = (255, 255, 255)
        self._text_color = (0, 0, 0)
        self._axis_color = (100, 100, 100)
        self._grid_color = (200, 200, 200)
        
        # 标志量
        self._initialized = False
        self._auto_range = True
        
        # 时间序列特有的属性
        self._line_styles = {}  # 存储每条线的样式
        self._symbol_styles = {}  # 存储每条线的点标记样式
        self._is_stepped = {}  # 存储每条线是否使用阶梯样式
        self._line_width = {}  # 存储每条线的宽度
        self._time_windows = {}  # 存储每条线的时间窗口
        
        # 创建日志前缀
        self._log_prefix = f"[{self.__class__.__name__}]"
        
        logger.debug(f"{self._log_prefix} 创建图表对象: {self.title}")
    
    def initialize(self, parent=None):
        """
        初始化图表组件
        
        创建并配置PyQtGraph组件。
        
        Args:
            parent: 父控件
        """
        if self._initialized:
            return
        
        try:
            # 创建时间轴
            time_axis = TimeAxisItem(orientation='bottom', format=self.time_format)
            
            # 创建绘图部件，使用自定义时间轴
            self._plot_widget = pg.PlotWidget(parent=parent, axisItems={'bottom': time_axis})
            
            # 设置基本属性
            self._plot_widget.setBackground(self._background_color)
            
            # 获取绘图项，用于进一步配置
            self._plot_item = self._plot_widget.getPlotItem()
            
            # 设置标题和轴标签
            self._plot_item.setTitle(self.title, color=self._text_color)
            self._plot_item.setLabel('bottom', self.x_label, color=self._text_color)
            self._plot_item.setLabel('left', self.y_label, color=self._text_color)
            
            # 创建图例
            self._legend = self._plot_item.addLegend()
            
            # 配置网格
            self._plot_item.showGrid(x=self._grid_enabled, y=self._grid_enabled, alpha=0.3)
            
            # 标记为已初始化
            self._initialized = True
            logger.debug(f"{self._log_prefix} 图表初始化完成")
            
            # 调用子类特定的初始化
            self._initialize_specific()
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 图表初始化失败: {str(e)}")
            raise
    
    def _initialize_specific(self):
        """实现图表特定的初始化"""
        # 时间序列图特有的初始化操作（如果有）
        pass
    
    def add_data(
        self, 
        data_id: str, 
        data: Union[Tuple[List, List], np.ndarray], 
        **kwargs
    ):
        """
        添加数据到时间序列图
        
        Args:
            data_id: 数据标识符
            data: 数据内容，可以是 (timestamps, values) 元组或二维数组
            **kwargs: 额外配置参数，包括：
                - name: 线条名称（显示在图例中）
                - color: 线条颜色，格式为 (R,G,B) 或 '#RRGGBB'
                - style: 线条样式，如 Qt.SolidLine, Qt.DashLine 等
                - symbol: 点标记样式，如 'o', 't', '+', None 等
                - symbol_size: 点标记大小
                - symbol_brush: 点标记填充颜色
                - stepped: 是否使用阶梯样式
                - line_width: 线条宽度
                - time_format: 时间格式化字符串
                - time_window: 时间窗口大小（秒），如果提供，只显示最近的数据
        """
        if not self._initialized:
            self.initialize()
        
        try:
            # 解析数据
            timestamps, values = self._parse_time_data(data)
            
            # 解析样式参数
            name = kwargs.get('name', data_id)
            color = kwargs.get('color', None)
            style = kwargs.get('style', None)
            symbol = kwargs.get('symbol', None)
            symbol_size = kwargs.get('symbol_size', 10)
            symbol_brush = kwargs.get('symbol_brush', color)
            stepped = kwargs.get('stepped', False)
            line_width = kwargs.get('line_width', 1)
            time_format = kwargs.get('time_format', None)
            time_window = kwargs.get('time_window', None)
            
            # 更新时间格式（如果提供）
            if time_format is not None:
                self.set_time_format(time_format)
            
            # 存储时间窗口设置
            if time_window is not None:
                self._time_windows[data_id] = time_window
            
            # 应用时间窗口
            if data_id in self._time_windows and self._time_windows[data_id] is not None:
                current_time = time.time()
                window_start = current_time - self._time_windows[data_id]
                # 筛选在时间窗口内的数据
                valid_indices = [i for i, ts in enumerate(timestamps) if ts >= window_start]
                if valid_indices:
                    timestamps = [timestamps[i] for i in valid_indices]
                    values = [values[i] for i in valid_indices]
            
            # 如果没有指定颜色，使用自动生成的颜色
            if color is None:
                # 使用预定义的颜色列表
                colors = [
                    (255, 0, 0),      # 红色
                    (0, 0, 255),      # 蓝色
                    (0, 255, 0),      # 绿色
                    (255, 165, 0),    # 橙色
                    (128, 0, 128),    # 紫色
                    (255, 192, 203),  # 粉红色
                    (165, 42, 42),    # 棕色
                    (0, 255, 255),    # 青色
                ]
                color = colors[len(self._data_items) % len(colors)]
            
            # 创建折线数据项
            pen = pg.mkPen(color=color, width=line_width)
            if style is not None:
                pen.setStyle(style)
            
            # 创建点标记配置
            symbol_opts = {}
            if symbol is not None:
                symbol_opts['symbol'] = symbol
                symbol_opts['symbolSize'] = symbol_size
                if symbol_brush is not None:
                    symbol_opts['symbolBrush'] = symbol_brush
            
            # 使用阶梯样式
            if stepped:
                connect = 'finite'  # 允许断点
                # 将数据转换为阶梯形式
                x_stepped = []
                y_stepped = []
                for i in range(len(timestamps) - 1):
                    x_stepped.extend([timestamps[i], timestamps[i+1]])
                    y_stepped.extend([values[i], values[i]])
                timestamps = x_stepped
                values = y_stepped
            else:
                connect = 'finite'  # 允许断点
            
            # 创建折线项
            plot_item = self._plot_item.plot(
                x=timestamps, 
                y=values, 
                pen=pen, 
                name=name,
                connect=connect,
                **symbol_opts
            )
            
            # 存储数据项和样式信息
            self._data_items[data_id] = plot_item
            self._line_styles[data_id] = style
            self._symbol_styles[data_id] = symbol
            self._is_stepped[data_id] = stepped
            self._line_width[data_id] = line_width
            
            logger.debug(f"{self._log_prefix} 添加时间序列: {data_id}, 数据点数: {len(timestamps)}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 添加数据失败: {str(e)}")
            raise
    
    def update_data(
        self, 
        data_id: str, 
        data: Union[Tuple[List, List], np.ndarray], 
        **kwargs
    ):
        """
        更新时间序列图中的数据
        
        Args:
            data_id: 数据标识符
            data: 新的数据内容
            **kwargs: 额外的样式参数，与add_data相同
        """
        if not self._initialized or data_id not in self._data_items:
            if data_id not in self._data_items:
                # 如果数据不存在，则添加新数据
                self.add_data(data_id, data, **kwargs)
            return
        
        try:
            # 解析数据
            timestamps, values = self._parse_time_data(data)
            
            # 获取线条项
            line_item = self._data_items[data_id]
            
            # 更新时间窗口设置
            if 'time_window' in kwargs:
                self._time_windows[data_id] = kwargs['time_window']
            
            # 应用时间窗口
            if data_id in self._time_windows and self._time_windows[data_id] is not None:
                current_time = time.time()
                window_start = current_time - self._time_windows[data_id]
                # 筛选在时间窗口内的数据
                valid_indices = [i for i, ts in enumerate(timestamps) if ts >= window_start]
                if valid_indices:
                    timestamps = [timestamps[i] for i in valid_indices]
                    values = [values[i] for i in valid_indices]
            
            # 检查是否更新样式
            if 'color' in kwargs or 'style' in kwargs or 'line_width' in kwargs:
                color = kwargs.get('color', line_item.opts['pen'].color())
                style = kwargs.get('style', self._line_styles.get(data_id))
                line_width = kwargs.get('line_width', self._line_width.get(data_id, 1))
                
                pen = pg.mkPen(color=color, width=line_width)
                if style is not None:
                    pen.setStyle(style)
                line_item.setPen(pen)
                
                self._line_styles[data_id] = style
                self._line_width[data_id] = line_width
            
            # 检查是否更新点标记样式
            if 'symbol' in kwargs or 'symbol_size' in kwargs or 'symbol_brush' in kwargs:
                symbol = kwargs.get('symbol', self._symbol_styles.get(data_id))
                symbol_size = kwargs.get('symbol_size', line_item.opts.get('symbolSize', 10))
                symbol_brush = kwargs.get('symbol_brush', line_item.opts.get('symbolBrush'))
                
                line_item.setSymbol(symbol)
                line_item.setSymbolSize(symbol_size)
                if symbol_brush is not None:
                    line_item.setSymbolBrush(symbol_brush)
                
                self._symbol_styles[data_id] = symbol
            
            # 检查是否需要转换为阶梯样式
            stepped = kwargs.get('stepped', self._is_stepped.get(data_id, False))
            if stepped != self._is_stepped.get(data_id, False):
                self._is_stepped[data_id] = stepped
                # 重新添加数据以应用阶梯样式
                self.clear_data(data_id)
                self.add_data(data_id, data, **kwargs)
                return
            
            # 更新数据
            line_item.setData(timestamps, values)
            
            logger.debug(f"{self._log_prefix} 更新时间序列: {data_id}, 数据点数: {len(timestamps)}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 更新数据失败: {str(e)}")
            raise
    
    def _parse_time_data(self, data) -> Tuple[List, List]:
        """
        解析时间数据格式
        
        Args:
            data: 输入数据，可以是 (timestamps, values) 元组或二维数组
            
        Returns:
            Tuple[List, List]: 时间戳和值列表
        """
        if isinstance(data, tuple) and len(data) == 2:
            # 如果是 (timestamps, values) 元组
            timestamps, values = data
            
            # 处理时间戳
            processed_timestamps = []
            for ts in timestamps:
                if isinstance(ts, (datetime.datetime, datetime.date)):
                    # 转换datetime对象为UNIX时间戳
                    processed_timestamps.append(ts.timestamp())
                elif isinstance(ts, (int, float)):
                    # 假设已经是时间戳
                    processed_timestamps.append(ts)
                else:
                    # 尝试解析字符串为datetime
                    try:
                        dt = datetime.datetime.fromisoformat(str(ts))
                        processed_timestamps.append(dt.timestamp())
                    except ValueError:
                        # 如果失败，使用当前时间
                        processed_timestamps.append(time.time())
            
            # 确保值是列表类型
            if isinstance(values, np.ndarray):
                values = values.tolist()
                
            return processed_timestamps, values
            
        elif isinstance(data, np.ndarray):
            # 如果是二维数组
            if data.ndim == 2 and data.shape[1] == 2:
                # 如果是 [[timestamp1, value1], [timestamp2, value2], ...] 格式
                timestamps = data[:, 0].tolist()
                values = data[:, 1].tolist()
                
                # 处理时间戳
                processed_timestamps = []
                for ts in timestamps:
                    if isinstance(ts, (datetime.datetime, datetime.date)):
                        processed_timestamps.append(ts.timestamp())
                    else:
                        processed_timestamps.append(ts)
                
                return processed_timestamps, values
        
        # 其他情况，使用当前时间作为时间戳
        if hasattr(data, '__iter__'):
            values = list(data)
            current_time = time.time()
            timestamps = [current_time - (len(values) - i - 1) for i in range(len(values))]
            return timestamps, values
        
        raise ValueError(f"不支持的数据格式: {type(data)}")
    
    def set_time_format(self, format_str: str):
        """
        设置时间轴格式
        
        Args:
            format_str: 时间格式化字符串，如 '%Y-%m-%d %H:%M:%S'
        """
        if not self._initialized:
            self.time_format = format_str
            return
        
        # 获取时间轴对象
        time_axis = self._plot_widget.getAxis('bottom')
        if isinstance(time_axis, TimeAxisItem):
            time_axis.set_format(format_str)
            # 重绘轴
            time_axis.update()
    
    def set_time_range(self, start_time, end_time):
        """
        设置时间轴范围
        
        Args:
            start_time: 开始时间（datetime或时间戳）
            end_time: 结束时间（datetime或时间戳）
        """
        if not self._initialized:
            return
        
        # 转换datetime对象为时间戳
        if isinstance(start_time, (datetime.datetime, datetime.date)):
            start_time = start_time.timestamp()
        if isinstance(end_time, (datetime.datetime, datetime.date)):
            end_time = end_time.timestamp()
        
        self._plot_item.setXRange(start_time, end_time)
        self._auto_range = False
    
    def set_time_window(self, data_id: str, window_size: float):
        """
        设置时间窗口大小
        
        Args:
            data_id: 数据标识符
            window_size: 窗口大小（秒）
        """
        self._time_windows[data_id] = window_size
        
        # 如果数据已存在，应用时间窗口
        if self._initialized and data_id in self._data_items:
            line_item = self._data_items[data_id]
            x_data, y_data = line_item.getData()
            
            if x_data is not None and len(x_data) > 0:
                current_time = time.time()
                window_start = current_time - window_size
                
                # 筛选在时间窗口内的数据
                valid_indices = [i for i, ts in enumerate(x_data) if ts >= window_start]
                if valid_indices:
                    new_x = [x_data[i] for i in valid_indices]
                    new_y = [y_data[i] for i in valid_indices]
                    line_item.setData(new_x, new_y)
    
    def set_auto_scroll(self, enabled: bool = True, window_size: float = 60.0):
        """
        设置自动滚动模式
        
        Args:
            enabled: 是否启用自动滚动
            window_size: 显示的时间窗口大小（秒）
        """
        if not self._initialized:
            return
        
        if enabled:
            # 禁用自动范围调整
            self._auto_range = False
            
            # 创建或获取更新定时器
            if not hasattr(self, '_scroll_timer'):
                self._scroll_timer = QtCore.QTimer()
                self._scroll_timer.timeout.connect(lambda: self._update_time_view(window_size))
            
            # 启动定时器
            self._scroll_timer.start(1000)  # 每秒更新一次
        else:
            # 停止定时器
            if hasattr(self, '_scroll_timer') and self._scroll_timer.isActive():
                self._scroll_timer.stop()
    
    def _update_time_view(self, window_size: float):
        """更新时间视图范围"""
        current_time = time.time()
        self._plot_item.setXRange(current_time - window_size, current_time)
    
    def add_real_time_point(self, data_id: str, value: float, timestamp=None):
        """
        添加实时数据点
        
        Args:
            data_id: 数据标识符
            value: 数据值
            timestamp: 时间戳，如果为None则使用当前时间
        """
        if not self._initialized:
            self.initialize()
        
        # 如果没有提供时间戳，使用当前时间
        if timestamp is None:
            timestamp = time.time()
        
        if data_id not in self._data_items:
            # 创建新的时间序列
            self.add_data(data_id, ([timestamp], [value]), name=data_id)
        else:
            # 获取现有数据
            line_item = self._data_items[data_id]
            x_data, y_data = line_item.getData()
            
            # 添加新点
            x_data = np.append(x_data, timestamp)
            y_data = np.append(y_data, value)
            
            # 应用时间窗口
            if data_id in self._time_windows and self._time_windows[data_id] is not None:
                window_start = timestamp - self._time_windows[data_id]
                # 保留在时间窗口内的点
                valid_indices = [i for i, ts in enumerate(x_data) if ts >= window_start]
                if valid_indices:
                    x_data = x_data[valid_indices]
                    y_data = y_data[valid_indices]
            
            # 更新数据
            line_item.setData(x_data, y_data)
