#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
综合图表框架示例

展示所有已实现的图表类型及其基本用法。
包括折线图、散点图、时间序列图、柱状图、热力图、饼图、箱线图和雷达图。
支持标签页布局和堆叠布局两种显示方式。
"""

import os
import sys
import time
import numpy as np
import traceback
from typing import List, Tuple, Dict
from datetime import datetime, timedelta

# 确保可以导入其他模块 - 更新路径为从根目录导入
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

# 尝试导入Qt库
try:
    from PyQt5.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget,
                               QPushButton, QHBoxLayout, QMessageBox, QTabWidget,
                               QLabel, QComboBox, QSpinBox, QDoubleSpinBox, QGroupBox,
                               QScrollArea, QSizePolicy, QFrame)
    from PyQt5.QtCore import QTimer, Qt, QSize
except ImportError:
    try:
        from PySide6.QtWidgets import (QApplication, QMainWindow, QVBoxLayout, QWidget,
                                     QPushButton, QHBoxLayout, QMessageBox, QTabWidget,
                                     QLabel, QComboBox, QSpinBox, QDoubleSpinBox, QGroupBox,
                                     QScrollArea, QSizePolicy, QFrame)
        from PySide6.QtCore import QTimer, Qt, QSize
    except ImportError:
        print("错误：找不到 PyQt5 或 PySide6 库。请安装其中一个。")
        sys.exit(1)

# 导入图表相关模块
from src.visualization.chart_manager import ChartManager, LayoutType
from src.visualization.charts.line_chart import LineChart
from src.visualization.charts.scatter import ScatterChart
from src.visualization.charts.time_series import TimeSeriesChart
from src.visualization.charts.histogram import HistogramChart
from src.visualization.charts.heatmap import HeatmapChart
# from src.visualization.charts.pie_chart import PieChart  # 暂时注释，存在实现问题
from src.visualization.charts.box_plot import BoxPlotChart
from src.visualization.charts.radar import RadarChart
from src.utils.logger import logger


class ChartExampleWindow(QMainWindow):
    """图表示例主窗口"""
    
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Data-Show 图表框架示例")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 创建主布局
        main_layout = QVBoxLayout(central_widget)
        
        # 创建标签页
        self.tab_widget = QTabWidget()
        main_layout.addWidget(self.tab_widget)
        
        # 初始化各个标签页
        self.init_line_chart_tab()
        self.init_scatter_chart_tab()
        self.init_time_series_tab()
        self.init_histogram_tab()
        self.init_heatmap_tab()
        # self.init_pie_chart_tab()  # 暂时跳过饼图，存在实现问题
        self.init_box_plot_tab()
        self.init_radar_chart_tab()
        
        logger.info("图表示例窗口初始化完成")
    
    def init_line_chart_tab(self):
        """初始化折线图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建折线图
        line_chart = LineChart(title="折线图示例", x_label="时间", y_label="数值")
        chart_id = chart_manager.add_chart(line_chart)

        # 创建示例数据
        x_data = np.linspace(0, 10, 100)
        y_data1 = np.sin(x_data)
        y_data2 = np.cos(x_data)
        y_data3 = np.sin(x_data) * np.cos(x_data)

        # 添加多条线
        line_chart.add_data("正弦波", (x_data, y_data1), color='red', line_width=2)
        line_chart.add_data("余弦波", (x_data, y_data2), color='blue', line_width=2)
        line_chart.add_data("正弦余弦乘积", (x_data, y_data3), color='green', line_width=2)

        # 创建控制面板
        control_panel = self.create_line_chart_controls(line_chart)
        layout.addWidget(control_panel)

        self.tab_widget.addTab(tab, "折线图")
    
    def create_line_chart_controls(self, chart):
        """创建折线图控制面板"""
        control_panel = QGroupBox("控制面板")
        layout = QHBoxLayout(control_panel)
        
        # 更新数据按钮
        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_line_chart_data(chart))
        layout.addWidget(update_btn)
        
        # 清除数据按钮
        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: chart.clear_data())
        layout.addWidget(clear_btn)
        
        return control_panel
    
    def update_line_chart_data(self, chart):
        """更新折线图数据"""
        try:
            # 创建新的随机数据
            x_data = np.linspace(0, 10, 100)
            y_data1 = np.sin(x_data + np.random.random())
            y_data2 = np.cos(x_data + np.random.random())
            y_data3 = np.sin(x_data + np.random.random()) * np.cos(x_data + np.random.random())

            # 更新数据
            chart.update_data("正弦波", (x_data, y_data1))
            chart.update_data("余弦波", (x_data, y_data2))
            chart.update_data("正弦余弦乘积", (x_data, y_data3))

            logger.info("已更新折线图数据")
        except Exception as e:
            logger.exception(f"更新折线图数据失败: {str(e)}")
    
    def init_scatter_chart_tab(self):
        """初始化散点图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建散点图
        scatter_chart = ScatterChart(title="散点图示例", x_label="X轴", y_label="Y轴")
        chart_id = chart_manager.add_chart(scatter_chart)

        # 创建示例数据
        n_points = 100
        x_data = np.random.randn(n_points)
        y_data = np.random.randn(n_points)
        colors = np.random.rand(n_points)
        sizes = np.random.randint(5, 20, n_points)

        # 添加散点数据
        scatter_chart.add_data(
            data_id="随机散点",
            data=(x_data, y_data),
            colors=colors,
            sizes=sizes,
            colormap='viridis'
        )

        # 创建控制面板
        control_panel = self.create_scatter_chart_controls(scatter_chart)
        layout.addWidget(control_panel)

        self.tab_widget.addTab(tab, "散点图")
    
    def create_scatter_chart_controls(self, chart):
        """创建散点图控制面板"""
        control_panel = QGroupBox("控制面板")
        layout = QHBoxLayout(control_panel)
        
        # 更新数据按钮
        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_scatter_chart_data(chart))
        layout.addWidget(update_btn)
        
        # 颜色映射选择
        colormap_combo = QComboBox()
        colormap_combo.addItems(['viridis', 'plasma', 'inferno', 'magma'])
        colormap_combo.currentIndexChanged.connect(lambda idx: self.change_scatter_colormap(chart, idx))
        layout.addWidget(QLabel("颜色映射:"))
        layout.addWidget(colormap_combo)
        
        return control_panel
    
    def update_scatter_chart_data(self, chart):
        """更新散点图数据"""
        try:
            # 创建新的随机数据
            n_points = 100
            x_data = np.random.randn(n_points)
            y_data = np.random.randn(n_points)
            colors = np.random.rand(n_points)
            sizes = np.random.randint(5, 20, n_points)

            # 更新数据
            chart.update_data(
                data_id="随机散点",
                data=(x_data, y_data),
                colors=colors,
                sizes=sizes
            )

            logger.info("已更新散点图数据")
        except Exception as e:
            logger.exception(f"更新散点图数据失败: {str(e)}")
    
    def change_scatter_colormap(self, chart, index):
        """更改散点图的颜色映射"""
        try:
            colormaps = ['viridis', 'plasma', 'inferno', 'magma']
            colormap = colormaps[index]
            
            # 重新添加数据以应用新的颜色映射
            n_points = 100
            x_data = np.random.randn(n_points)
            y_data = np.random.randn(n_points)
            colors = np.random.rand(n_points)
            sizes = np.random.randint(5, 20, n_points)

            chart.update_data(
                data_id="随机散点",
                data=(x_data, y_data),
                colors=colors,
                sizes=sizes,
                colormap=colormap
            )

            logger.info(f"已更改散点图颜色映射为: {colormap}")
        except Exception as e:
            logger.exception(f"更改散点图颜色映射失败: {str(e)}")
    
    def init_time_series_tab(self):
        """初始化时间序列图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建时间序列图
        time_series_chart = TimeSeriesChart(title="时间序列图示例", x_label="时间", y_label="数值")
        chart_id = chart_manager.add_chart(time_series_chart)

        # 创建示例时间序列数据
        start_time = datetime.now() - timedelta(days=30)
        timestamps = [start_time + timedelta(hours=i) for i in range(720)]  # 30天，每小时一个点
        values1 = np.sin(np.linspace(0, 4*np.pi, len(timestamps))) + np.random.normal(0, 0.1, len(timestamps))
        values2 = np.cos(np.linspace(0, 4*np.pi, len(timestamps))) + np.random.normal(0, 0.1, len(timestamps))

        # 添加时间序列数据
        time_series_chart.add_data("传感器1", (timestamps, values1), color='red')
        time_series_chart.add_data("传感器2", (timestamps, values2), color='blue')

        # 创建控制面板
        control_panel = self.create_time_series_controls(time_series_chart)
        layout.addWidget(control_panel)

        self.tab_widget.addTab(tab, "时间序列图")
    
    def create_time_series_controls(self, chart):
        """创建时间序列图控制面板"""
        control_panel = QGroupBox("控制面板")
        layout = QHBoxLayout(control_panel)
        
        # 更新数据按钮
        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_time_series_data(chart))
        layout.addWidget(update_btn)
        
        # 添加实时数据按钮
        realtime_btn = QPushButton("添加实时数据")
        realtime_btn.clicked.connect(lambda: self.add_realtime_data(chart))
        layout.addWidget(realtime_btn)
        
        return control_panel
    
    def update_time_series_data(self, chart):
        """更新时间序列数据"""
        try:
            # 创建新的时间序列数据
            start_time = datetime.now() - timedelta(days=30)
            timestamps = [start_time + timedelta(hours=i) for i in range(720)]
            values1 = np.sin(np.linspace(0, 4*np.pi, len(timestamps))) + np.random.normal(0, 0.2, len(timestamps))
            values2 = np.cos(np.linspace(0, 4*np.pi, len(timestamps))) + np.random.normal(0, 0.2, len(timestamps))

            # 更新数据
            chart.update_data("传感器1", (timestamps, values1))
            chart.update_data("传感器2", (timestamps, values2))

            logger.info("已更新时间序列数据")
        except Exception as e:
            logger.exception(f"更新时间序列数据失败: {str(e)}")
    
    def add_realtime_data(self, chart):
        """添加实时数据点"""
        try:
            # 生成随机数据值
            value1 = np.random.normal(0, 1)
            value2 = np.random.normal(0, 1)

            # 使用add_real_time_point方法添加实时数据（时间戳自动使用当前时间）
            chart.add_real_time_point("传感器1", value1)
            chart.add_real_time_point("传感器2", value2)

            logger.info(f"已添加实时数据: 传感器1={value1:.2f}, 传感器2={value2:.2f}")
        except Exception as e:
            logger.exception(f"添加实时数据失败: {str(e)}")

    def init_histogram_tab(self):
        """初始化柱状图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建柱状图
        histogram_chart = HistogramChart(title="柱状图示例", x_label="类别", y_label="数值")
        chart_id = chart_manager.add_chart(histogram_chart)

        # 创建示例数据
        categories = ['A', 'B', 'C', 'D', 'E']
        values1 = [23, 45, 56, 78, 32]
        values2 = [34, 25, 67, 45, 56]

        # 添加柱状图数据
        histogram_chart.add_data("系列1", values1, categories=categories, color='blue')
        histogram_chart.add_data("系列2", values2, categories=categories, color='red')

        # 创建控制面板
        control_panel = self.create_histogram_controls(histogram_chart)
        layout.addWidget(control_panel)

        self.tab_widget.addTab(tab, "柱状图")

    def create_histogram_controls(self, chart):
        """创建柱状图控制面板"""
        control_panel = QGroupBox("控制面板")
        layout = QHBoxLayout(control_panel)

        # 更新数据按钮
        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_histogram_data(chart))
        layout.addWidget(update_btn)

        # 清除数据按钮
        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: chart.clear_data())
        layout.addWidget(clear_btn)

        return control_panel

    def update_histogram_data(self, chart):
        """更新柱状图数据"""
        try:
            # 创建新的随机数据
            categories = ['A', 'B', 'C', 'D', 'E']
            values1 = np.random.randint(10, 100, len(categories)).tolist()
            values2 = np.random.randint(10, 100, len(categories)).tolist()

            # 更新数据
            chart.update_data("系列1", values1, categories=categories)
            chart.update_data("系列2", values2, categories=categories)

            logger.info("已更新柱状图数据")
        except Exception as e:
            logger.exception(f"更新柱状图数据失败: {str(e)}")

    def init_heatmap_tab(self):
        """初始化热力图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建热力图
        heatmap_chart = HeatmapChart(title="热力图示例", x_label="X轴", y_label="Y轴")
        chart_id = chart_manager.add_chart(heatmap_chart)

        # 创建示例数据 - 20x20的热力图
        data = np.zeros((20, 20))
        # 添加一些模式
        for i in range(20):
            for j in range(20):
                # 创建一些有趣的图案
                data[i, j] = np.sin(i/5.0) * np.cos(j/5.0) * 0.5 + 0.5

        # X和Y轴标签
        x_labels = [f"X{i}" for i in range(20)]
        y_labels = [f"Y{i}" for i in range(20)]

        # 添加热力图数据
        heatmap_chart.add_data(
            data_id="热力图数据",
            data=data,
            x_labels=x_labels,
            y_labels=y_labels
        )

        # 创建控制面板
        control_panel = self.create_heatmap_controls(heatmap_chart, x_labels, y_labels)
        layout.addWidget(control_panel)

        # 保存对象以便后续使用
        self.heatmap_chart = heatmap_chart

        # 添加到标签页
        self.tab_widget.addTab(tab, "热力图")

    def create_heatmap_controls(self, chart, x_labels, y_labels):
        """创建热力图控制面板"""
        control_panel = QGroupBox("控制面板")
        layout = QHBoxLayout(control_panel)

        # 更新数据按钮
        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_heatmap_data(chart, x_labels, y_labels))
        layout.addWidget(update_btn)

        # 颜色映射选择
        color_map_combo = QComboBox()
        color_map_combo.addItems(["viridis", "plasma", "inferno", "magma"])
        color_map_combo.currentIndexChanged.connect(
            lambda idx: self.change_heatmap_colormap(chart, idx)
        )
        layout.addWidget(QLabel("颜色映射:"))
        layout.addWidget(color_map_combo)

        # 清除数据按钮
        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: chart.clear_data())
        layout.addWidget(clear_btn)

        return control_panel

    def update_heatmap_data(self, chart, x_labels, y_labels):
        """更新热力图数据 - 保持平滑效果"""
        try:
            # 使用不同的数学函数生成平滑图案
            data = np.zeros((20, 20))
            phase = np.random.random() * 2 * np.pi  # 随机相位

            for i in range(20):
                for j in range(20):
                    # 创建不同的平滑图案
                    data[i, j] = np.sin(i/3.0 + phase) * np.cos(j/4.0 + phase) * 0.5 + 0.5
                    # 添加其他数学图案增加复杂度
                    data[i, j] += np.sin((i+j)/6.0 + phase) * 0.2
                    # 添加径向波纹效果
                    dx = i - 10
                    dy = j - 10
                    r = np.sqrt(dx*dx + dy*dy)
                    data[i, j] += np.sin(r/2.0 + phase) * 0.1

            # 更新热力图数据
            chart.update_data(
                data_id="热力图数据",
                data=data,
                x_labels=x_labels,
                y_labels=y_labels
            )

            logger.info("已更新热力图数据 - 使用平滑数学图案")
        except Exception as e:
            logger.exception(f"更新热力图数据失败: {str(e)}")

    def change_heatmap_colormap(self, chart, index):
        """更改热力图的颜色映射"""
        try:
            colormaps = ["viridis", "plasma", "inferno", "magma"]
            colormap = colormaps[index]

            chart.set_colormap(colormap)

            logger.info(f"已更改热力图颜色映射为: {colormap}")
        except Exception as e:
            logger.exception(f"更改热力图颜色映射失败: {str(e)}")

    def init_pie_chart_tab(self):
        """初始化饼图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建饼图
        pie_chart = PieChart(title="饼图示例")
        chart_id = chart_manager.add_chart(pie_chart)

        # 创建示例数据
        data = {
            '苹果': 30,
            '香蕉': 25,
            '橙子': 20,
            '葡萄': 15,
            '草莓': 10
        }
        colors = ['red', 'yellow', 'orange', 'purple', 'pink']

        # 添加饼图数据
        pie_chart.add_data("水果销量", data, colors=colors)

        # 创建控制面板
        control_panel = self.create_pie_chart_controls(pie_chart)
        layout.addWidget(control_panel)

        self.tab_widget.addTab(tab, "饼图")

    def create_pie_chart_controls(self, chart):
        """创建饼图控制面板"""
        control_panel = QGroupBox("控制面板")
        layout = QHBoxLayout(control_panel)

        # 更新数据按钮
        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_pie_chart_data(chart))
        layout.addWidget(update_btn)

        # 清除数据按钮
        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: chart.clear_data())
        layout.addWidget(clear_btn)

        return control_panel

    def update_pie_chart_data(self, chart):
        """更新饼图数据"""
        try:
            # 创建新的随机数据
            data = {
                '苹果': np.random.randint(10, 50),
                '香蕉': np.random.randint(10, 50),
                '橙子': np.random.randint(10, 50),
                '葡萄': np.random.randint(10, 50),
                '草莓': np.random.randint(10, 50)
            }
            colors = ['red', 'yellow', 'orange', 'purple', 'pink']

            # 更新数据
            chart.update_data("水果销量", data, colors=colors)

            logger.info("已更新饼图数据")
        except Exception as e:
            logger.exception(f"更新饼图数据失败: {str(e)}")

    def init_box_plot_tab(self):
        """初始化箱线图标签页"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建箱线图
        box_plot_chart = BoxPlotChart(title="箱线图示例", x_label="组别", y_label="数值")
        chart_id = chart_manager.add_chart(box_plot_chart)

        # 创建示例数据 - 使用字典格式以正确显示多个组别
        box_data = {
            "组A": np.random.normal(50, 15, 100),
            "组B": np.random.normal(60, 10, 100),
            "组C": np.random.normal(55, 20, 100)
        }

        # 添加箱线图数据 - 使用字典格式一次性添加所有组别
        box_plot_chart.add_data("箱线图数据", box_data)

        # 创建控制面板
        control_panel = self.create_box_plot_controls(box_plot_chart)
        layout.addWidget(control_panel)

        self.tab_widget.addTab(tab, "箱线图")

    def create_box_plot_controls(self, chart):
        """创建箱线图控制面板"""
        control_panel = QGroupBox("控制面板")
        layout = QVBoxLayout(control_panel)

        # 第一行：数据控制
        data_row = QHBoxLayout()

        # 更新数据按钮
        update_btn = QPushButton("更新数据")
        update_btn.clicked.connect(lambda: self.update_box_plot_data(chart))
        data_row.addWidget(update_btn)

        # 清除数据按钮
        clear_btn = QPushButton("清除数据")
        clear_btn.clicked.connect(lambda: chart.clear_data())
        data_row.addWidget(clear_btn)

        layout.addLayout(data_row)

        # 第二行：显示控制
        display_row = QHBoxLayout()

        # 显示/隐藏异常值
        outliers_btn = QPushButton("隐藏异常值")
        outliers_btn.setCheckable(True)
        outliers_btn.clicked.connect(lambda checked: self.toggle_outliers(chart, outliers_btn, checked))
        display_row.addWidget(outliers_btn)

        # 显示/隐藏图例
        legend_btn = QPushButton("隐藏图例")
        legend_btn.setCheckable(True)
        legend_btn.clicked.connect(lambda checked: self.toggle_legend(chart, legend_btn, checked))
        display_row.addWidget(legend_btn)

        layout.addLayout(display_row)

        return control_panel

    def toggle_outliers(self, chart, button, checked):
        """切换异常值显示"""
        try:
            chart.set_show_outliers(not checked)
            button.setText("显示异常值" if checked else "隐藏异常值")
            logger.info(f"异常值显示: {'隐藏' if checked else '显示'}")
        except Exception as e:
            logger.exception(f"切换异常值显示失败: {str(e)}")

    def toggle_legend(self, chart, button, checked):
        """切换图例显示"""
        try:
            chart.set_show_legend(not checked)
            button.setText("显示图例" if checked else "隐藏图例")
            logger.info(f"图例显示: {'隐藏' if checked else '显示'}")
        except Exception as e:
            logger.exception(f"切换图例显示失败: {str(e)}")

    def update_box_plot_data(self, chart):
        """更新箱线图数据"""
        try:
            # 创建新的随机数据 - 使用字典格式
            new_box_data = {
                "组A": np.random.normal(45, 12, 100),  # 稍微不同的参数
                "组B": np.random.normal(65, 8, 100),
                "组C": np.random.normal(50, 18, 100)
            }

            # 更新数据
            chart.update_data("箱线图数据", new_box_data)

            logger.info("已更新箱线图数据")
        except Exception as e:
            logger.exception(f"更新箱线图数据失败: {str(e)}")

    def init_radar_chart_tab(self):
        """初始化雷达图标签页 - 展示新的Matplotlib实现"""
        tab = QWidget()
        layout = QVBoxLayout(tab)

        # 创建图表管理器
        chart_manager = ChartManager(layout_type=LayoutType.GRID)
        layout.addWidget(chart_manager)

        # 创建雷达图 - 使用新的Matplotlib实现（自动选择最佳实现）
        radar_chart = RadarChart(
            title="员工能力评估雷达图 - 新实现展示",
            prefer_matplotlib=True,  # 优先使用Matplotlib实现
            use_smart_text=False     # 使用传统文本渲染，避免文本方向问题
        )
        chart_id = chart_manager.add_chart(radar_chart)

        # 创建更丰富的示例数据 - 展示中文标签支持
        dimensions = [
            '技术专业能力',
            '团队协作沟通',
            '持续学习成长',
            '创新思维能力',
            '项目执行力',
            '领导管理能力'
        ]

        # 多个员工的能力数据
        employees_data = {
            '张三 (新员工)': [75, 80, 90, 65, 70, 50],
            '李四 (资深员工)': [90, 85, 75, 85, 90, 80],
            '王五 (团队领导)': [85, 95, 70, 90, 85, 95],
            '赵六 (技术专家)': [95, 70, 85, 80, 75, 70]
        }

        # 定义颜色方案
        colors = [
            (255, 100, 100),  # 红色系
            (100, 150, 255),  # 蓝色系
            (100, 200, 100),  # 绿色系
            (255, 150, 50)    # 橙色系
        ]

        # 添加雷达图数据 - 展示多系列支持
        for i, (name, values) in enumerate(employees_data.items()):
            radar_chart.add_data(
                name,
                values,
                categories=dimensions,
                color=colors[i],
                fill_alpha=60
            )

        # 设置最大值
        radar_chart.set_max_value(100)

        # 创建控制面板
        control_panel = self.create_radar_chart_controls(radar_chart, dimensions, employees_data)
        layout.addWidget(control_panel)

        # 保存引用以便控制面板使用
        self.radar_chart = radar_chart
        self.radar_dimensions = dimensions
        self.radar_employees_data = employees_data

        self.tab_widget.addTab(tab, "雷达图 (新实现)")

    def create_radar_chart_controls(self, chart, dimensions, employees_data):
        """创建雷达图控制面板 - 展示新功能"""
        control_panel = QGroupBox("雷达图控制面板 - 新功能展示")
        layout = QVBoxLayout(control_panel)

        # 第一行：基础数据控制
        data_row = QHBoxLayout()

        # 更新数据按钮
        update_btn = QPushButton("🔄 更新随机数据")
        update_btn.clicked.connect(lambda: self.update_radar_chart_data(chart, dimensions, employees_data))
        data_row.addWidget(update_btn)

        # 添加新员工按钮
        add_employee_btn = QPushButton("➕ 添加新员工")
        add_employee_btn.clicked.connect(lambda: self.add_new_employee(chart, dimensions))
        data_row.addWidget(add_employee_btn)

        # 清除数据按钮
        clear_btn = QPushButton("🗑️ 清除所有数据")
        clear_btn.clicked.connect(lambda: chart.clear_data())
        data_row.addWidget(clear_btn)

        layout.addLayout(data_row)

        # 第二行：显示控制
        display_row = QHBoxLayout()

        # 显示实现信息按钮
        info_btn = QPushButton("ℹ️ 显示实现信息")
        info_btn.clicked.connect(lambda: self.show_implementation_info(chart))
        display_row.addWidget(info_btn)

        # 测试文本重叠场景按钮
        overlap_test_btn = QPushButton("🔍 测试文本重叠场景")
        overlap_test_btn.clicked.connect(lambda: self.test_text_overlap_scenario(chart))
        display_row.addWidget(overlap_test_btn)

        # 展示多系列对比按钮
        comparison_btn = QPushButton("📊 展示团队对比")
        comparison_btn.clicked.connect(lambda: self.show_team_comparison(chart, dimensions))
        display_row.addWidget(comparison_btn)

        layout.addLayout(display_row)

        # 第三行：高级功能
        advanced_row = QHBoxLayout()

        # 切换到PyQtGraph实现按钮（如果可用）
        fallback_btn = QPushButton("🔄 切换到PyQtGraph实现")
        fallback_btn.clicked.connect(lambda: self.switch_to_pyqtgraph(chart, dimensions, employees_data))
        advanced_row.addWidget(fallback_btn)

        # 保存图片按钮
        save_btn = QPushButton("💾 保存图片")
        save_btn.clicked.connect(lambda: self.save_radar_chart(chart))
        advanced_row.addWidget(save_btn)

        layout.addLayout(advanced_row)

        return control_panel

    def update_radar_chart_data(self, chart, dimensions, employees_data):
        """更新雷达图数据 - 展示新实现的数据更新能力"""
        try:
            # 为每个员工创建新的随机数据
            colors = [
                (255, 100, 100),  # 红色系
                (100, 150, 255),  # 蓝色系
                (100, 200, 100),  # 绿色系
                (255, 150, 50)    # 橙色系
            ]

            for i, name in enumerate(employees_data.keys()):
                # 生成新的随机能力值（保持一定的合理性）
                base_values = list(employees_data[name])
                new_values = []

                for base_val in base_values:
                    # 在原值基础上添加随机变化（±15）
                    variation = np.random.randint(-15, 16)
                    new_val = max(30, min(100, base_val + variation))  # 限制在30-100范围内
                    new_values.append(new_val)

                # 更新数据
                chart.update_data(
                    name,
                    new_values,
                    categories=dimensions,
                    color=colors[i % len(colors)]
                )

            logger.info("已更新雷达图数据 - 展示数据动态变化")
        except Exception as e:
            logger.exception(f"更新雷达图数据失败: {str(e)}")

    def add_new_employee(self, chart, dimensions):
        """添加新员工数据 - 展示动态添加功能"""
        try:
            # 生成新员工名称
            new_names = ["钱七 (实习生)", "孙八 (产品经理)", "周九 (设计师)", "吴十 (测试工程师)"]
            existing_count = len([name for name in chart.data_series.keys() if any(new_name.split()[0] in name for new_name in new_names)])

            if existing_count < len(new_names):
                new_name = new_names[existing_count]

                # 生成随机能力值
                new_values = np.random.randint(40, 95, len(dimensions)).tolist()

                # 选择颜色
                colors = [(255, 200, 100), (150, 100, 255), (255, 100, 200), (100, 255, 150)]
                color = colors[existing_count % len(colors)]

                # 添加新员工数据
                chart.add_data(
                    new_name,
                    new_values,
                    categories=dimensions,
                    name=new_name,
                    color=color,
                    fill_alpha=60
                )

                logger.info(f"已添加新员工: {new_name}")
            else:
                logger.info("已达到最大员工数量")

        except Exception as e:
            logger.exception(f"添加新员工失败: {str(e)}")

    def show_implementation_info(self, chart):
        """显示当前雷达图实现信息"""
        try:
            # 获取实现信息
            if hasattr(chart, 'get_implementation_info'):
                info = chart.get_implementation_info()

                info_text = f"""
雷达图实现信息：

🔧 当前实现: {info.get('implementation', '未知')}
📊 图形库: {info.get('backend', '未知')}
🎨 智能文本渲染: {'✅ 支持' if info.get('features', {}).get('smart_text_rendering', False) else '❌ 不支持'}
📈 多系列支持: {'✅ 支持' if info.get('features', {}).get('multi_series', False) else '❌ 不支持'}
🎯 数据标准化: {'✅ 支持' if info.get('features', {}).get('data_normalization', False) else '❌ 不支持'}
🌈 主题配置: {'✅ 支持' if info.get('features', {}).get('theme_support', False) else '❌ 不支持'}

✨ 新实现特性:
• 完美的中文字体支持
• 智能文本定位，避免重叠
• 从顶部开始的正确雷达图方向
• 多系列数据对比功能
• 高级样式和主题配置
• 完全兼容原有接口
                """
            else:
                info_text = """
雷达图实现信息：

⚠️ 当前使用的是旧版本实现
建议升级到新的Matplotlib实现以获得更好的功能和性能。
                """

            # 显示信息对话框
            msg_box = QMessageBox()
            msg_box.setWindowTitle("雷达图实现信息")
            msg_box.setText(info_text)
            msg_box.setIcon(QMessageBox.Information)
            msg_box.exec_()

        except Exception as e:
            logger.exception(f"显示实现信息失败: {str(e)}")

    def test_text_overlap_scenario(self, chart):
        """测试文本重叠场景 - 展示问题解决能力"""
        try:
            # 创建容易重叠的长标签
            long_dimensions = [
                '软件开发技术专业能力综合评估',
                '跨部门团队协作沟通交流能力',
                '持续学习自我提升成长能力',
                '创新思维问题解决分析能力',
                '项目管理执行推进协调能力',
                '客户服务需求理解响应能力'
            ]

            # 清除现有数据
            chart.clear_data()

            # 设置新的类别
            chart.set_categories(long_dimensions)

            # 添加测试数据
            test_values = [85, 78, 92, 75, 88, 82]
            chart.add_data(
                "文本重叠测试",
                test_values,
                categories=long_dimensions,
                name="长标签测试员工",
                color=(255, 100, 100),
                fill_alpha=60
            )

            logger.info("已切换到文本重叠测试场景 - 展示智能文本定位")

        except Exception as e:
            logger.exception(f"测试文本重叠场景失败: {str(e)}")

    def show_team_comparison(self, chart, dimensions):
        """展示团队对比 - 恢复多系列显示"""
        try:
            # 清除现有数据
            chart.clear_data()

            # 设置原始类别
            chart.set_categories(dimensions)

            # 恢复团队数据
            team_data = {
                '前端团队平均': [85, 80, 85, 70, 80, 75],
                '后端团队平均': [90, 75, 80, 85, 85, 80],
                '测试团队平均': [75, 85, 90, 80, 75, 85],
                '产品团队平均': [70, 95, 85, 75, 90, 90]
            }

            colors = [
                (255, 150, 150),  # 浅红色
                (150, 150, 255),  # 浅蓝色
                (150, 255, 150),  # 浅绿色
                (255, 200, 100)   # 浅橙色
            ]

            for i, (team_name, values) in enumerate(team_data.items()):
                chart.add_data(
                    team_name,
                    values,
                    categories=dimensions,
                    name=team_name,
                    color=colors[i],
                    fill_alpha=50
                )

            logger.info("已切换到团队对比模式 - 展示多系列功能")

        except Exception as e:
            logger.exception(f"展示团队对比失败: {str(e)}")

    def switch_to_pyqtgraph(self, chart, dimensions, employees_data):
        """切换到PyQtGraph实现（如果可用）"""
        try:
            # 这里可以实现切换逻辑
            # 由于集成适配器的存在，实际上会自动选择最佳实现
            logger.info("当前已使用最佳可用实现")

            # 显示提示信息
            msg_box = QMessageBox()
            msg_box.setWindowTitle("实现切换")
            msg_box.setText("""
🔄 实现切换说明：

新的雷达图实现使用智能集成适配器，会自动选择最佳可用的实现：

1️⃣ 优先使用 Matplotlib 实现（推荐）
   • 完美的中文字体支持
   • 智能文本定位
   • 专业的图形渲染

2️⃣ 自动回退到 PyQtGraph 实现
   • 当 Matplotlib 不可用时
   • 保持向后兼容性

✨ 无需手动切换，系统会自动选择最佳方案！
            """)
            msg_box.setIcon(QMessageBox.Information)
            msg_box.exec_()

        except Exception as e:
            logger.exception(f"切换实现失败: {str(e)}")

    def save_radar_chart(self, chart):
        """保存雷达图为图片"""
        try:
            if hasattr(chart, 'save_chart'):
                # 使用时间戳创建文件名
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                filename = f"radar_chart_example_{timestamp}.png"

                chart.save_chart(filename, dpi=200)
                logger.info(f"雷达图已保存为: {filename}")

                # 显示成功消息
                msg_box = QMessageBox()
                msg_box.setWindowTitle("保存成功")
                msg_box.setText(f"雷达图已成功保存为:\n{filename}")
                msg_box.setIcon(QMessageBox.Information)
                msg_box.exec_()
            else:
                logger.warning("当前实现不支持保存功能")

        except Exception as e:
            logger.exception(f"保存雷达图失败: {str(e)}")


def main():
    """主函数"""
    try:
        # 创建应用程序
        app = QApplication(sys.argv)

        # 创建主窗口
        window = ChartExampleWindow()
        window.show()

        # 运行应用程序
        sys.exit(app.exec_())

    except Exception as e:
        logger.exception(f"应用程序启动失败: {str(e)}")
        print(f"错误: {str(e)}")
        traceback.print_exc()


if __name__ == "__main__":
    main()
