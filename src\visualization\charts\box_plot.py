#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
箱线图模块

实现基于PyQtGraph的箱线图，支持多组数据、自定义样式和异常值显示等功能。
"""

import numpy as np
from typing import Dict, List, Optional, Tuple, Union, Any

try:
    import pyqtgraph as pg
    from pyqtgraph.Qt import QtCore, QtGui
except ImportError:
    # 如果导入失败，可能会在运行时另行处理
    pass

from ..base_chart import BaseChart, ChartType
from ...utils.logger import logger


class BoxPlotItem(pg.GraphicsObject):
    """箱线图项目"""
    
    def __init__(
        self, 
        x: float, 
        box_data: Tuple[float, float, float, float, float], 
        width: float = 0.7, 
        whisker_width: float = 0.5,
        color: Tuple[int, int, int] = (0, 0, 255),
        outliers: List[float] = None,
        name: str = "",
        pen_width: float = 1.0
    ):
        """
        初始化箱线图项目
        
        Args:
            x: X轴位置
            box_data: 箱线图数据，格式为 (最小值, 下四分位数, 中位数, 上四分位数, 最大值)
            width: 箱体宽度
            whisker_width: 触须宽度
            color: 箱体颜色
            outliers: 异常值列表
            name: 名称
            pen_width: 线条宽度
        """
        super().__init__()
        
        self.x = x
        self.box_data = box_data
        self.whisker_min, self.q1, self.median, self.q3, self.whisker_max = box_data
        self.width = width
        self.whisker_width = whisker_width
        self.color = color
        self.outliers = outliers or []
        self._original_outliers = self.outliers.copy()  # 保存原始异常值
        self.name = name
        self.pen_width = pen_width
        self._show_outliers = True  # 添加显示异常值的标志
        
        # 创建画笔和画刷
        self.pen = pg.mkPen(color=color, width=pen_width)
        self.median_pen = pg.mkPen(color=(0, 0, 0), width=pen_width * 1.5)
        self.whisker_pen = pg.mkPen(color=color, width=pen_width)
        
        # 为异常值创建特殊的画笔和画刷，统一使用深红色
        outlier_color = (139, 0, 0, 200)  # 深红色 (DarkRed) 带透明度
        self.outlier_pen = pg.mkPen(color=(139, 0, 0), width=max(1.5, pen_width * 1.2))  # 深红色画笔
        self.outlier_brush = pg.mkBrush(color=outlier_color)  # 深红色实心填充画刷
        
        self.brush = pg.mkBrush(color=(*color[:3], 80))  # 箱体适度透明填充
        
        # 生成路径
        self.path = self._create_path()
        self.median_path = self._create_median_path()
        self.whisker_paths = self._create_whisker_paths()
    
    def _create_path(self):
        """创建箱体路径"""
        path = QtGui.QPainterPath()
        
        # 计算箱体的边界
        half_width = self.width / 2
        left = self.x - half_width
        right = self.x + half_width
        
        # 创建箱体矩形
        path.addRect(QtCore.QRectF(left, self.q1, self.width, self.q3 - self.q1))
        
        return path
    
    def _create_median_path(self):
        """创建中位数线路径"""
        path = QtGui.QPainterPath()
        
        # 计算箱体的边界
        half_width = self.width / 2
        left = self.x - half_width
        right = self.x + half_width
        
        # 创建中位数线
        path.moveTo(left, self.median)
        path.lineTo(right, self.median)
        
        return path
    
    def _create_whisker_paths(self):
        """创建触须路径"""
        paths = []
        
        # 计算触须的宽度
        half_width = self.width / 2
        half_whisker_width = self.width * self.whisker_width / 2
        left = self.x - half_whisker_width
        right = self.x + half_whisker_width
        center_left = self.x - half_width
        center_right = self.x + half_width
        
        # 上触须
        upper_path = QtGui.QPainterPath()
        upper_path.moveTo(self.x, self.q3)
        upper_path.lineTo(self.x, self.whisker_max)
        upper_path.moveTo(left, self.whisker_max)
        upper_path.lineTo(right, self.whisker_max)
        paths.append(upper_path)
        
        # 下触须
        lower_path = QtGui.QPainterPath()
        lower_path.moveTo(self.x, self.q1)
        lower_path.lineTo(self.x, self.whisker_min)
        lower_path.moveTo(left, self.whisker_min)
        lower_path.lineTo(right, self.whisker_min)
        paths.append(lower_path)
        
        return paths
    
    def boundingRect(self):
        """获取包围盒"""
        # 计算所有元素的包围盒
        rect = self.path.boundingRect()
        
        # 包含触须
        rect = rect.united(QtCore.QRectF(
            self.x - self.width/2, self.whisker_min,
            self.width, self.whisker_max - self.whisker_min
        ))
        
        # 包含异常值
        if self.outliers:
            outlier_min = min(self.outliers)
            outlier_max = max(self.outliers)
            rect = rect.united(QtCore.QRectF(
                self.x - self.width/2, outlier_min,
                self.width, outlier_max - outlier_min
            ))
        
        return rect
    
    def paint(self, painter, option, widget=None):
        """绘制箱线图"""
        # 绘制箱体
        painter.setPen(self.pen)
        painter.setBrush(self.brush)
        painter.drawPath(self.path)
        
        # 绘制中位数线
        painter.setPen(self.median_pen)
        painter.drawPath(self.median_path)
        
        # 绘制触须
        painter.setPen(self.whisker_pen)
        for path in self.whisker_paths:
            painter.drawPath(path)
        
        # 绘制异常值
        if self._show_outliers and self.outliers:
            # 设置异常值的画笔和画刷
            painter.setPen(self.outlier_pen)
            painter.setBrush(self.outlier_brush)

            # 异常值标记大小 - 按用户要求设置
            outlier_size = 16  # 直径16像素

            for outlier in self.outliers:
                # 绘制圆形异常值标记 - 使用设备坐标确保真正的圆形
                # 保存painter状态
                painter.save()

                # 将数据坐标转换为设备坐标
                data_point = QtCore.QPointF(self.x, outlier)
                device_point = painter.transform().map(data_point)

                # 重置变换，使用设备坐标
                painter.resetTransform()

                # 在设备坐标系中绘制圆形
                rect = QtCore.QRectF(
                    device_point.x() - outlier_size/2,
                    device_point.y() - outlier_size/2,
                    outlier_size,
                    outlier_size
                )
                painter.drawEllipse(rect)

                # 恢复painter状态
                painter.restore()
    
    def set_show_outliers(self, show: bool):
        """设置是否显示异常值"""
        self._show_outliers = show
        # 触发重绘
        self.update()


class BoxPlotChart(BaseChart):
    """
    箱线图类
    
    实现箱线图的创建、数据管理和样式设置等功能。
    """
    
    def __init__(
        self, 
        title: str = "箱线图", 
        x_label: str = "类别", 
        y_label: str = "数值"
    ):
        """
        初始化箱线图
        
        Args:
            title: 图表标题
            x_label: X轴标签
            y_label: Y轴标签
        """
        super().__init__(title, x_label, y_label, ChartType.BOX_PLOT)
        
        # 箱线图特有的属性
        self._box_items = {}  # 存储箱线图项目
        self._categories = []  # 存储类别标签
        self._show_outliers = True  # 是否显示异常值
        self._box_width = 0.7  # 箱体宽度
        self._whisker_width = 0.5  # 触须宽度
        self._box_colors = {}  # 存储箱体颜色
        self._legend = None  # 图例对象
        self._show_legend = True  # 是否显示图例
    
    def _initialize_specific(self):
        """实现图表特定的初始化"""
        # 设置x轴显示为分类模式
        self._plot_item.getAxis('bottom').setTicks([[], []])  # 清除自动生成的刻度

        # 初始化图例
        if self._show_legend:
            self._legend = self._plot_item.addLegend()
            self._legend.setOffset((10, 10))  # 设置图例位置偏移
    
    def set_categories(self, categories: List[str]):
        """
        设置类别标签
        
        Args:
            categories: 类别名称列表
        """
        self._categories = categories
        
        if not self._initialized:
            return
        
        try:
            # 更新x轴刻度 - 修改格式以确保每项只包含两个元素
            ticks = []
            for i, cat in enumerate(categories):
                # 确保类别标签是字符串类型
                if not isinstance(cat, str):
                    cat = str(cat)
                # 创建二元组 (位置, 标签)
                ticks.append((i, cat))
            
            # 使用二维列表包装ticks，PyQtGraph需要这种格式
            self._plot_item.getAxis('bottom').setTicks([ticks])
            
            # 调整x轴范围
            self._plot_item.setXRange(-0.5, len(categories) - 0.5)
            
            logger.debug(f"{self._log_prefix} 设置类别标签: {categories}")
        except Exception as e:
            logger.error(f"{self._log_prefix} 设置类别标签失败: {str(e)}")
    
    def calculate_box_data(self, data: Union[List, np.ndarray], method: str = 'tukey') -> Tuple[float, float, float, float, float, List]:
        """
        计算箱线图所需的统计数据
        
        Args:
            data: 输入数据
            method: 异常值检测方法，'tukey'（基于IQR）或'percentile'（基于百分位数）
            
        Returns:
            Tuple[float, float, float, float, float, List]: 
                (最小非异常值, 下四分位数, 中位数, 上四分位数, 最大非异常值, 异常值列表)
        """
        # 确保数据是NumPy数组
        if not isinstance(data, np.ndarray):
            data = np.array(data)
        
        # 移除NaN值
        data = data[~np.isnan(data)]
        
        if len(data) == 0:
            return (0, 0, 0, 0, 0, [])
        
        # 计算四分位数和中位数
        q1 = np.percentile(data, 25)
        median = np.percentile(data, 50)
        q3 = np.percentile(data, 75)
        
        # 计算四分位距
        iqr = q3 - q1
        
        # 检测异常值
        outliers = []
        if method == 'tukey':
            # Tukey方法：异常值定义为小于Q1-1.5*IQR或大于Q3+1.5*IQR的值
            lower_bound = q1 - 1.5 * iqr
            upper_bound = q3 + 1.5 * iqr
            
            outliers = data[(data < lower_bound) | (data > upper_bound)].tolist()
            
            # 计算实际的触须（非异常值的最小值和最大值）
            non_outliers = data[(data >= lower_bound) & (data <= upper_bound)]
            whisker_min = np.min(non_outliers) if len(non_outliers) > 0 else q1
            whisker_max = np.max(non_outliers) if len(non_outliers) > 0 else q3
            
        elif method == 'percentile':
            # 百分位数方法：触须为第5和第95百分位数，超出则为异常值
            whisker_min = np.percentile(data, 5)
            whisker_max = np.percentile(data, 95)
            
            outliers = data[(data < whisker_min) | (data > whisker_max)].tolist()
            
        else:
            # 默认方法：不检测异常值，触须为最小值和最大值
            whisker_min = np.min(data)
            whisker_max = np.max(data)
        
        return (whisker_min, q1, median, q3, whisker_max, outliers)
    
    def add_data(
        self, 
        data_id: str, 
        data: Union[np.ndarray, List, Dict[str, Union[List, np.ndarray]]], 
        **kwargs
    ):
        """
        添加数据到箱线图
        
        Args:
            data_id: 数据标识符
            data: 数据内容，可以是单个数组或字典 {类别: 数据数组}
            **kwargs: 额外配置参数，包括：
                - name: 数据系列名称（显示在图例中）
                - color: 箱体颜色，格式为 (R,G,B) 或 '#RRGGBB'
                - box_width: 箱体宽度，默认为0.7
                - whisker_width: 触须宽度，默认为0.5
                - categories: 类别标签列表（当data为字典时使用）
                - show_outliers: 是否显示异常值
                - method: 异常值检测方法，'tukey'或'percentile'
                - position: 箱体位置，用于多组数据
        """
        if not self._initialized:
            self.initialize()
        
        try:
            # 解析参数
            name = kwargs.get('name', data_id)
            color = kwargs.get('color', None)
            box_width = kwargs.get('box_width', self._box_width)
            whisker_width = kwargs.get('whisker_width', self._whisker_width)
            categories = kwargs.get('categories', None)
            show_outliers = kwargs.get('show_outliers', self._show_outliers)
            method = kwargs.get('method', 'tukey')
            position = kwargs.get('position', 0)
            
            # 处理颜色
            if color is None:
                # 使用专业的Material Design配色方案
                colors = [
                    (52, 152, 219),   # 专业蓝 - Blue
                    (231, 76, 60),    # 专业红 - Red
                    (46, 204, 113),   # 专业绿 - Green
                    (241, 196, 15),   # 专业黄 - Yellow
                    (155, 89, 182),   # 专业紫 - Purple
                    (230, 126, 34),   # 专业橙 - Orange
                    (52, 73, 94),     # 专业深蓝 - Dark Blue
                    (149, 165, 166),  # 专业灰 - Gray
                ]
                color = colors[len(self._data_items) % len(colors)]
            
            # 存储设置
            self._box_width = box_width
            self._whisker_width = whisker_width
            self._show_outliers = show_outliers
            self._box_colors[data_id] = color
            
            # 处理数据
            if isinstance(data, dict):
                # 数据是字典 {类别: 数据数组}
                if categories is None:
                    categories = list(data.keys())
                
                # 设置类别标签
                self.set_categories(categories)
                
                # 为每个类别创建箱线图
                for i, category in enumerate(categories):
                    if category in data:
                        category_data = data[category]
                        x_pos = i + (position * box_width * 1.2) if position != 0 else i
                        
                        # 计算箱线图数据
                        box_stats = self.calculate_box_data(category_data, method)
                        whisker_min, q1, median, q3, whisker_max, outliers = box_stats
                        
                        # 仅在显示异常值时包含它们
                        box_outliers = outliers if show_outliers else []
                        
                        # 创建箱线图项
                        box_item = BoxPlotItem(
                            x=x_pos,
                            box_data=(whisker_min, q1, median, q3, whisker_max),
                            width=box_width,
                            whisker_width=whisker_width,
                            color=color,
                            outliers=box_outliers,
                            name=f"{name} ({category})" if len(categories) > 1 else name
                        )
                        
                        # 添加到绘图项
                        self._plot_item.addItem(box_item)
                        
                        # 存储箱线图项
                        item_id = f"{data_id}_{i}"
                        self._box_items[item_id] = box_item
                        self._data_items[item_id] = box_item
                
                # 关联数据ID和所有箱线图项
                self._data_items[data_id] = list(self._box_items.values())
                
            else:
                # 数据是单个数组，创建单个箱线图
                # 计算箱线图数据
                box_stats = self.calculate_box_data(data, method)
                whisker_min, q1, median, q3, whisker_max, outliers = box_stats
                
                # 仅在显示异常值时包含它们
                box_outliers = outliers if show_outliers else []
                
                # 创建箱线图项
                box_item = BoxPlotItem(
                    x=position,
                    box_data=(whisker_min, q1, median, q3, whisker_max),
                    width=box_width,
                    whisker_width=whisker_width,
                    color=color,
                    outliers=box_outliers,
                    name=name
                )
                
                # 添加到绘图项
                self._plot_item.addItem(box_item)
                
                # 存储箱线图项
                self._box_items[data_id] = box_item
                self._data_items[data_id] = box_item
                
                # 如果没有类别，设置一个默认类别
                if not self._categories:
                    self.set_categories([name])
            
            # 调整Y轴范围以显示所有数据
            self.auto_range()

            # 更新图例
            self._update_legend()

            logger.debug(f"{self._log_prefix} 添加箱线图数据: {data_id}")
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 添加数据失败: {str(e)}")
            raise
    
    def update_data(
        self, 
        data_id: str, 
        data: Union[np.ndarray, List, Dict[str, Union[List, np.ndarray]]], 
        **kwargs
    ):
        """
        更新箱线图中的数据
        
        Args:
            data_id: 数据标识符
            data: 新的数据内容
            **kwargs: 额外配置参数，与add_data相同
        """
        # 如果数据不存在，则添加新数据
        if data_id not in self._data_items:
            self.add_data(data_id, data, **kwargs)
            return
        
        # 清除现有数据
        self.clear_data(data_id)
        
        # 添加新数据
        self.add_data(data_id, data, **kwargs)
    
    def set_box_width(self, data_id: str, width: float):
        """
        设置箱体宽度
        
        Args:
            data_id: 数据标识符
            width: 箱体宽度
        """
        if data_id not in self._data_items:
            return
        
        # 获取所有相关的箱线图项
        box_items = self._data_items[data_id]
        if not isinstance(box_items, list):
            box_items = [box_items]
        
        # 更新每个箱线图项的宽度
        for box_item in box_items:
            if isinstance(box_item, BoxPlotItem):
                # 移除旧的项目
                self._plot_item.removeItem(box_item)
                
                # 创建新的项目，替换旧的
                new_box_item = BoxPlotItem(
                    x=box_item.x,
                    box_data=box_item.box_data,
                    width=width,
                    whisker_width=box_item.whisker_width,
                    color=box_item.color,
                    outliers=box_item.outliers,
                    name=box_item.name
                )
                
                # 添加新项目
                self._plot_item.addItem(new_box_item)
                
                # 更新存储
                for key, value in self._box_items.items():
                    if value == box_item:
                        self._box_items[key] = new_box_item
                        break
                
                for key, value in self._data_items.items():
                    if isinstance(value, list):
                        for i, item in enumerate(value):
                            if item == box_item:
                                value[i] = new_box_item
                    elif value == box_item:
                        self._data_items[key] = new_box_item
        
        # 更新设置
        self._box_width = width
    
    def set_whisker_width(self, data_id: str, width: float):
        """
        设置触须宽度
        
        Args:
            data_id: 数据标识符
            width: 触须宽度
        """
        if data_id not in self._data_items:
            return
        
        # 获取所有相关的箱线图项
        box_items = self._data_items[data_id]
        if not isinstance(box_items, list):
            box_items = [box_items]
        
        # 更新每个箱线图项的触须宽度
        for box_item in box_items:
            if isinstance(box_item, BoxPlotItem):
                # 移除旧的项目
                self._plot_item.removeItem(box_item)
                
                # 创建新的项目，替换旧的
                new_box_item = BoxPlotItem(
                    x=box_item.x,
                    box_data=box_item.box_data,
                    width=box_item.width,
                    whisker_width=width,
                    color=box_item.color,
                    outliers=box_item.outliers,
                    name=box_item.name
                )
                
                # 添加新项目
                self._plot_item.addItem(new_box_item)
                
                # 更新存储
                for key, value in self._box_items.items():
                    if value == box_item:
                        self._box_items[key] = new_box_item
                        break
                
                for key, value in self._data_items.items():
                    if isinstance(value, list):
                        for i, item in enumerate(value):
                            if item == box_item:
                                value[i] = new_box_item
                    elif value == box_item:
                        self._data_items[key] = new_box_item
        
        # 更新设置
        self._whisker_width = width
    
    def set_show_outliers(self, show: bool):
        """
        设置是否显示异常值
        
        Args:
            show: 是否显示
        """
        if self._show_outliers == show:
            return
        
        self._show_outliers = show
        
        # 更新所有箱线图项
        for data_id, box_items in self._data_items.items():
            if isinstance(box_items, list):
                for box_item in box_items:
                    if isinstance(box_item, BoxPlotItem):
                        box_item.set_show_outliers(show)
            elif isinstance(box_items, BoxPlotItem):
                box_items.set_show_outliers(show)
        
        # 重新绘制
        self._plot_item.update()  # 使用正确的更新方法
    
    def set_box_color(self, data_id: str, color):
        """
        设置箱体颜色
        
        Args:
            data_id: 数据标识符
            color: 颜色，格式为 (R,G,B) 或 '#RRGGBB'
        """
        if data_id not in self._data_items:
            return
        
        # 更新颜色设置
        self._box_colors[data_id] = color
        
        # 获取所有相关的箱线图项
        box_items = self._data_items[data_id]
        if not isinstance(box_items, list):
            box_items = [box_items]
        
        # 更新每个箱线图项的颜色
        for box_item in box_items:
            if isinstance(box_item, BoxPlotItem):
                # 移除旧的项目
                self._plot_item.removeItem(box_item)
                
                # 创建新的项目，替换旧的
                new_box_item = BoxPlotItem(
                    x=box_item.x,
                    box_data=box_item.box_data,
                    width=box_item.width,
                    whisker_width=box_item.whisker_width,
                    color=color,
                    outliers=box_item.outliers,
                    name=box_item.name
                )
                
                # 添加新项目
                self._plot_item.addItem(new_box_item)
                
                # 更新存储
                for key, value in self._box_items.items():
                    if value == box_item:
                        self._box_items[key] = new_box_item
                        break
                
                for key, value in self._data_items.items():
                    if isinstance(value, list):
                        for i, item in enumerate(value):
                            if item == box_item:
                                value[i] = new_box_item
                    elif value == box_item:
                        self._data_items[key] = new_box_item
    
    def _update_legend(self):
        """更新图例显示"""
        if not self._show_legend or not self._legend:
            return

        try:
            # 清除现有图例项
            self._legend.clear()

            # 为每个数据组添加图例项
            for data_id, color in self._box_colors.items():
                if data_id in self._data_items:
                    # 创建一个简单的线条作为图例标记
                    legend_item = pg.PlotDataItem(
                        [0], [0],
                        pen=pg.mkPen(color=color, width=3),
                        name=data_id
                    )
                    self._legend.addItem(legend_item, data_id)

        except Exception as e:
            logger.warning(f"{self._log_prefix} 更新图例失败: {str(e)}")

    def set_show_legend(self, show: bool):
        """设置是否显示图例"""
        if self._show_legend == show:
            return

        self._show_legend = show

        if not self._initialized:
            return

        try:
            if show:
                if not self._legend:
                    # 创建图例
                    self._legend = self._plot_item.addLegend()
                    self._legend.setOffset((10, 10))
                    self._update_legend()
                    logger.debug(f"{self._log_prefix} 图例已创建并显示")
                else:
                    # 显示已存在的图例
                    self._legend.setVisible(True)
                    self._update_legend()
                    logger.debug(f"{self._log_prefix} 图例已显示")

            else:
                if self._legend:
                    # 隐藏图例而不删除
                    self._legend.setVisible(False)
                    logger.debug(f"{self._log_prefix} 图例已隐藏")

        except Exception as e:
            logger.error(f"{self._log_prefix} 设置图例显示失败: {str(e)}")
            # 重置状态但不抛出异常
            self._show_legend = not show

    def auto_range(self):
        """自动调整坐标范围以显示所有数据"""
        min_y = float('inf')
        max_y = float('-inf')

        # 查找所有数据的范围
        for box_item in self._box_items.values():
            # 考虑箱体数据
            min_y = min(min_y, box_item.whisker_min)
            max_y = max(max_y, box_item.whisker_max)

            # 考虑异常值
            if box_item.outliers:
                min_y = min(min_y, min(box_item.outliers))
                max_y = max(max_y, max(box_item.outliers))

        if min_y != float('inf') and max_y != float('-inf'):
            # 添加一些余量
            range_y = max_y - min_y
            min_y = min_y - range_y * 0.1
            max_y = max_y + range_y * 0.1

            # 设置Y轴范围
            self._plot_item.setYRange(min_y, max_y)

            # 设置X轴范围
            if self._categories:
                self._plot_item.setXRange(-0.5, len(self._categories) - 0.5)
            else:
                self._plot_item.setXRange(-0.5, 0.5)
    
    def get_stats(self, data_id: str) -> Dict[str, Any]:
        """
        获取指定数据的统计信息
        
        Args:
            data_id: 数据标识符
            
        Returns:
            Dict[str, Any]: 统计信息
        """
        if data_id not in self._data_items:
            return {}
        
        box_items = self._data_items[data_id]
        if not isinstance(box_items, list):
            box_items = [box_items]
        
        result = {}
        for box_item in box_items:
            if isinstance(box_item, BoxPlotItem):
                stats = {
                    'min': box_item.whisker_min,
                    'q1': box_item.q1,
                    'median': box_item.median,
                    'q3': box_item.q3,
                    'max': box_item.whisker_max,
                    'outliers': box_item.outliers
                }
                result[box_item.name] = stats
        
        return result
    
    def clear_data(self, data_id: Optional[str] = None):
        """
        清除图表数据

        Args:
            data_id: 要清除的数据标识符，如果为None则清除所有数据
        """
        try:
            # 在调用父类方法前，先确保从plot_item中移除所有相关的BoxPlotItem
            if data_id is None:
                # 清除所有BoxPlotItem
                for box_item in self._box_items.values():
                    self._plot_item.removeItem(box_item)

                # 清除图例
                if self._legend:
                    try:
                        self._legend.clear()
                    except Exception as e:
                        logger.warning(f"{self._log_prefix} 清除图例失败: {str(e)}")

                # 重置X轴标签
                self._categories = []
                self._plot_item.getAxis('bottom').setTicks([[], []])

                # 清除所有状态
                self._box_items = {}
                self._box_colors = {}

                logger.debug(f"{self._log_prefix} 清除所有数据")

            else:
                # 清除指定ID的BoxPlotItem
                keys_to_remove = []
                for key, box_item in self._box_items.items():
                    if key == data_id or key.startswith(f"{data_id}_"):
                        self._plot_item.removeItem(box_item)
                        keys_to_remove.append(key)

                # 清除指定ID的状态
                for key in keys_to_remove:
                    if key in self._box_items:
                        del self._box_items[key]
                    if key in self._box_colors:
                        del self._box_colors[key]

                logger.debug(f"{self._log_prefix} 清除数据: {data_id}")

            # 调用父类方法清除数据项
            super().clear_data(data_id)

            # 更新图例
            if self._legend and self._show_legend:
                self._update_legend()

            # 重新调整范围
            if self._box_items:
                self.auto_range()
            else:
                # 如果没有数据，重置视图范围
                self._plot_item.setXRange(-0.5, 0.5)
                self._plot_item.setYRange(0, 100)

        except Exception as e:
            logger.error(f"{self._log_prefix} 清除数据失败: {str(e)}")
            raise
