#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
图表基类模块

为所有类型的图表提供统一的基类和接口。
包括创建、配置、数据管理、样式设置等通用功能。
"""

import abc
from enum import Enum, auto
from typing import Dict, List, Optional, Tuple, Union, Any

import numpy as np
try:
    import pyqtgraph as pg
    from pyqtgraph.Qt import QtCore, QtGui
except ImportError:
    # 如果导入失败，可能会在运行时另行处理
    pg = None
    QtCore = None
    QtGui = None

from ..utils.logger import logger


class ChartType(Enum):
    """图表类型枚举"""
    LINE = auto()
    SCATTER = auto()
    BAR = auto()
    TIME_SERIES = auto()
    HEATMAP = auto()
    PIE = auto()
    BOX_PLOT = auto()
    RADAR = auto()
    CUSTOM = auto()


class BaseChart(abc.ABC):
    """
    图表基类
    
    定义所有图表类型共享的属性和方法。
    子类必须实现抽象方法。
    """
    
    def __init__(
        self, 
        title: str = "", 
        x_label: str = "", 
        y_label: str = "", 
        chart_type: ChartType = ChartType.CUSTOM
    ):
        """
        初始化图表
        
        Args:
            title: 图表标题
            x_label: X轴标签
            y_label: Y轴标签
            chart_type: 图表类型
        """
        self.title = title
        self.x_label = x_label
        self.y_label = y_label
        self.chart_type = chart_type
        
        # 图表布局和数据相关
        self._plot_widget = None
        self._plot_item = None
        self._legend = None
        self._grid_enabled = True
        self._data_items = {}  # 存储所有数据项目（如曲线、散点等）
        
        # 样式相关
        self._background_color = (255, 255, 255)
        self._text_color = (0, 0, 0)
        self._axis_color = (100, 100, 100)
        self._grid_color = (200, 200, 200)
        
        # 标志量
        self._initialized = False
        self._auto_range = True
        
        # 创建日志前缀
        self._log_prefix = f"[{self.__class__.__name__}]"
        
        logger.debug(f"{self._log_prefix} 创建图表对象: {self.title}")
    
    def initialize(self, parent=None):
        """
        初始化图表组件
        
        创建并配置PyQtGraph组件。
        
        Args:
            parent: 父控件
        """
        if self._initialized:
            return
        
        try:
            # 检查pyqtgraph是否可用
            if pg is None:
                raise ImportError("PyQtGraph is not available")

            # 创建绘图部件
            self._plot_widget = pg.PlotWidget(parent=parent)
            
            # 设置基本属性
            self._plot_widget.setBackground(self._background_color)
            
            # 获取绘图项，用于进一步配置
            self._plot_item = self._plot_widget.getPlotItem()
            
            # 设置标题和轴标签
            self._plot_item.setTitle(self.title, color=self._text_color)
            self._plot_item.setLabel('bottom', self.x_label, color=self._text_color)
            self._plot_item.setLabel('left', self.y_label, color=self._text_color)
            
            # 创建图例
            self._legend = self._plot_item.addLegend()
            
            # 配置网格
            self._plot_item.showGrid(x=self._grid_enabled, y=self._grid_enabled, alpha=0.3)
            
            # 标记为已初始化
            self._initialized = True
            logger.debug(f"{self._log_prefix} 图表初始化完成")
            
            # 调用子类特定的初始化
            self._initialize_specific()
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 图表初始化失败: {str(e)}")
            raise
    
    @property
    def widget(self):
        """获取图表控件"""
        if not self._initialized:
            self.initialize()
        return self._plot_widget
    
    def get_widget(self):
        """获取图表控件（兼容性方法）"""
        return self.widget
    
    @abc.abstractmethod
    def _initialize_specific(self):
        """
        特定类型图表的初始化
        
        此方法必须由子类实现，用于配置特定类型图表的特殊属性。
        """
        pass
    
    @abc.abstractmethod
    def add_data(self, data_id: str, data: Any, **kwargs):
        """
        添加数据到图表
        
        Args:
            data_id: 数据标识符
            data: 数据内容，格式由具体图表类型定义
            **kwargs: 额外配置参数
        """
        pass
    
    @abc.abstractmethod
    def update_data(self, data_id: str, data: Any, **kwargs):
        """
        更新图表中的数据
        
        Args:
            data_id: 数据标识符
            data: 数据内容，格式由具体图表类型定义
            **kwargs: 额外配置参数
        """
        pass
    
    def clear_data(self, data_id: Optional[str] = None):
        """
        清除图表数据
        
        Args:
            data_id: 要清除的数据标识符，如果为None则清除所有数据
        """
        if not self._initialized:
            return
        
        try:
            if data_id is None:
                # 清除所有数据
                for item_id in list(self._data_items.keys()):
                    self._remove_data_item(item_id)
                
                # 清空数据项字典
                self._data_items.clear()
                logger.debug(f"{self._log_prefix} 清除所有数据")
            
            elif data_id in self._data_items:
                # 清除特定数据
                self._remove_data_item(data_id)
                logger.debug(f"{self._log_prefix} 清除数据: {data_id}")
        
        except Exception as e:
            logger.error(f"{self._log_prefix} 清除数据失败: {str(e)}")
    
    def _remove_data_item(self, data_id: str):
        """
        从图表中移除数据项
        
        Args:
            data_id: 数据标识符
        """
        if data_id in self._data_items:
            item = self._data_items[data_id]
            if self._plot_item is not None:
                self._plot_item.removeItem(item)
            del self._data_items[data_id]
    
    def set_title(self, title: str):
        """设置图表标题"""
        self.title = title
        if self._initialized and self._plot_item is not None:
            self._plot_item.setTitle(title, color=self._text_color)
    
    def set_axis_labels(self, x_label: Optional[str] = None, y_label: Optional[str] = None):
        """设置坐标轴标签"""
        if x_label is not None:
            self.x_label = x_label
            if self._initialized and self._plot_item is not None:
                self._plot_item.setLabel('bottom', x_label, color=self._text_color)
        
        if y_label is not None:
            self.y_label = y_label
            if self._initialized and self._plot_item is not None:
                self._plot_item.setLabel('left', y_label, color=self._text_color)
    
    def set_background_color(self, color: Tuple[int, int, int]):
        """
        设置背景颜色
        
        Args:
            color: RGB颜色元组 (R,G,B)
        """
        self._background_color = color
        if self._initialized and self._plot_widget is not None:
            self._plot_widget.setBackground(color)
    
    def set_grid_visible(self, visible: bool):
        """设置网格可见性"""
        self._grid_enabled = visible
        if self._initialized and self._plot_item is not None:
            self._plot_item.showGrid(x=visible, y=visible, alpha=0.3)
    
    def set_auto_range(self, enabled: bool):
        """设置自动调整范围"""
        self._auto_range = enabled
        if self._initialized and self._plot_item is not None:
            if enabled:
                self._plot_item.enableAutoRange()
            else:
                self._plot_item.disableAutoRange()
    
    def export_image(self, file_path: str, width: Optional[int] = None, height: Optional[int] = None):
        """
        导出图表为图像文件
        
        Args:
            file_path: 输出文件路径
            width: 图像宽度（像素）
            height: 图像高度（像素）
        """
        if not self._initialized:
            logger.error(f"{self._log_prefix} 导出图像失败: 图表未初始化")
            return False
        
        try:
            # 获取导出器
            exporter = pg.exporters.ImageExporter(self._plot_item)
            
            # 设置导出尺寸
            if width is not None and height is not None:
                exporter.parameters()['width'] = width
                exporter.parameters()['height'] = height
            
            # 导出图像
            exporter.export(file_path)
            logger.info(f"{self._log_prefix} 图表已导出为图像: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"{self._log_prefix} 导出图像失败: {str(e)}")
            return False
    
    def on_click(self, callback):
        """
        设置点击事件回调
        
        Args:
            callback: 回调函数，接收点击位置坐标
        """
        if not self._initialized:
            logger.warning(f"{self._log_prefix} 设置点击事件失败: 图表未初始化")
            return
        
        def _mouse_clicked(event):
            """鼠标点击事件处理"""
            pos = event.pos()
            if self._plot_item is not None:
                # 转换屏幕坐标到数据坐标
                view_box = self._plot_item.getViewBox()
                scene_coords = self._plot_widget.mapToScene(pos)
                data_coords = view_box.mapSceneToView(scene_coords)
                callback(data_coords.x(), data_coords.y())
        
        # 连接鼠标点击事件
        self._plot_widget.scene().sigMouseClicked.connect(_mouse_clicked)
    
    def get_data_ids(self):
        """获取所有数据标识符"""
        return list(self._data_items.keys())
    
    def has_data(self, data_id: str) -> bool:
        """检查是否有指定数据"""
        return data_id in self._data_items
    
    def set_data_visible(self, data_id: str, visible: bool):
        """
        设置数据可见性
        
        Args:
            data_id: 数据标识符
            visible: 是否可见
        """
        if not self._initialized or data_id not in self._data_items:
            return
        
        try:
            item = self._data_items[data_id]
            item.setVisible(visible)
        except Exception as e:
            logger.error(f"{self._log_prefix} 设置数据可见性失败: {str(e)}")
    
    def __str__(self):
        """返回图表的字符串表示"""
        return f"{self.__class__.__name__}(title='{self.title}', type={self.chart_type.name})" 