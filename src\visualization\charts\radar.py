#!/usr/bin/env python
# -*- coding: utf-8 -*-

"""
雷达图模块 - 统一实现

集成了Matplotlib实现、配置管理、字体管理和智能文本渲染功能。
解决文本重叠和中文显示问题，提供完整的雷达图解决方案。
"""

import numpy as np
import math

import platform
import os
import json
from typing import Dict, List, Optional, Tuple, Union, Any
from dataclasses import dataclass, field
from enum import Enum

# PyQtGraph导入（用于回退实现）
try:
    import pyqtgraph as pg
    from pyqtgraph.Qt import QtCore, QtGui
    PYQTGRAPH_AVAILABLE = True
except ImportError:
    PYQTGRAPH_AVAILABLE = False

from ..base_chart import BaseChart, ChartType
from ...utils.logger import logger

# 检查Matplotlib可用性
try:
    import matplotlib
    import matplotlib.pyplot as plt
    import matplotlib.font_manager as fm
    import matplotlib.patches as patches
    from matplotlib.figure import Figure
    from matplotlib.backends.backend_qt5agg import FigureCanvasQTAgg as FigureCanvas
    MATPLOTLIB_AVAILABLE = True
except ImportError as e:
    print(f"Warning: Matplotlib not available: {e}")
    MATPLOTLIB_AVAILABLE = False
    # 创建占位符以避免NameError
    plt = None
    fm = None
    patches = None
    Figure = None
    FigureCanvas = None


# ============================================================================
# 配置系统
# ============================================================================

class ColorScheme(Enum):
    """颜色方案枚举"""
    DEFAULT = "default"
    PROFESSIONAL = "professional"
    VIBRANT = "vibrant"
    PASTEL = "pastel"
    MONOCHROME = "monochrome"
    CUSTOM = "custom"


class ChartTheme(Enum):
    """图表主题枚举"""
    LIGHT = "light"
    DARK = "dark"
    MINIMAL = "minimal"
    CORPORATE = "corporate"
    SCIENTIFIC = "scientific"


@dataclass
class FontConfig:
    """字体配置"""
    family: str = 'Microsoft YaHei'
    size: float = 12
    weight: str = 'normal'  # 'normal', 'bold'
    style: str = 'normal'   # 'normal', 'italic'

    def to_dict(self) -> Dict[str, Any]:
        return {
            'fontfamily': self.family,
            'fontsize': self.size,
            'fontweight': self.weight,
            'fontstyle': self.style
        }


@dataclass
class LineConfig:
    """线条配置"""
    width: float = 2.0
    style: str = '-'        # '-', '--', '-.', ':'
    alpha: float = 0.8

    def to_dict(self) -> Dict[str, Any]:
        return {
            'linewidth': self.width,
            'linestyle': self.style,
            'alpha': self.alpha
        }


@dataclass
class MarkerConfig:
    """标记配置"""
    style: str = 'o'        # 'o', 's', '^', 'v', 'D', '*'
    size: float = 6
    edge_width: float = 1.0
    edge_color: str = 'auto'  # 'auto' 或具体颜色

    def to_dict(self) -> Dict[str, Any]:
        return {
            'marker': self.style,
            'markersize': self.size,
            'markeredgewidth': self.edge_width,
            'markeredgecolor': self.edge_color
        }


@dataclass
class FillConfig:
    """填充配置"""
    alpha: float = 0.25
    enabled: bool = True

    def to_dict(self) -> Dict[str, Any]:
        return {
            'alpha': self.alpha,
            'enabled': self.enabled
        }


@dataclass
class GridConfig:
    """网格配置"""
    enabled: bool = True
    lines: int = 5
    alpha: float = 0.3
    color: str = '#cccccc'
    line_width: float = 0.5

    def to_dict(self) -> Dict[str, Any]:
        return {
            'enabled': self.enabled,
            'lines': self.lines,
            'alpha': self.alpha,
            'color': self.color,
            'linewidth': self.line_width
        }


class RadarChartConfig:
    """
    雷达图配置管理器

    功能：
    1. 预定义主题管理
    2. 颜色方案配置
    3. 字体和样式设置
    4. 配置文件保存/加载
    """

    def __init__(self, theme: ChartTheme = ChartTheme.LIGHT,
                 color_scheme: ColorScheme = ColorScheme.DEFAULT):
        """
        初始化配置管理器

        Args:
            theme: 图表主题
            color_scheme: 颜色方案
        """
        self._log_prefix = "[RadarChartConfig]"

        # 基础配置
        self.theme = theme
        self.color_scheme = color_scheme

        # 配置组件
        self.font = FontConfig()
        self.line = LineConfig()
        self.marker = MarkerConfig()
        self.fill = FillConfig()
        self.grid = GridConfig()

        # 图形配置
        self.figure_size = (8, 8)
        self.dpi = 100
        self.background_color = '#ffffff'
        self.text_color = '#000000'

        # 颜色配置
        self.colors = self._get_default_colors()

        # 智能文本配置
        self.smart_text_enabled = True
        self.text_collision_margin = 0.02
        self.text_radial_offset = 0.1

        # 应用主题
        self._apply_theme()
        self._apply_color_scheme()

        logger.debug(f"{self._log_prefix} 配置管理器初始化完成: {theme.value}/{color_scheme.value}")

    def _get_default_colors(self) -> List[str]:
        """获取默认颜色列表"""
        return [
            '#1f77b4', '#ff7f0e', '#2ca02c', '#d62728', '#9467bd',
            '#8c564b', '#e377c2', '#7f7f7f', '#bcbd22', '#17becf'
        ]

    def _apply_theme(self):
        """应用主题设置"""
        if self.theme == ChartTheme.LIGHT:
            self.background_color = '#ffffff'
            self.text_color = '#000000'
            self.grid.color = '#cccccc'
        elif self.theme == ChartTheme.DARK:
            self.background_color = '#2e2e2e'
            self.text_color = '#ffffff'
            self.grid.color = '#555555'
        elif self.theme == ChartTheme.MINIMAL:
            self.background_color = '#ffffff'
            self.text_color = '#333333'
            self.grid.alpha = 0.2
        elif self.theme == ChartTheme.CORPORATE:
            self.background_color = '#f8f9fa'
            self.text_color = '#212529'
            self.colors = ['#007bff', '#28a745', '#ffc107', '#dc3545', '#6f42c1']
        elif self.theme == ChartTheme.SCIENTIFIC:
            self.background_color = '#ffffff'
            self.text_color = '#000000'
            self.grid.lines = 10
            self.grid.alpha = 0.4

        logger.debug(f"{self._log_prefix} 应用主题: {self.theme.value}")

    def _apply_color_scheme(self):
        """应用颜色方案"""
        if self.color_scheme == ColorScheme.PROFESSIONAL:
            self.colors = ['#2E86AB', '#A23B72', '#F18F01', '#C73E1D', '#592E83']
        elif self.color_scheme == ColorScheme.VIBRANT:
            self.colors = ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FFEAA7']
        elif self.color_scheme == ColorScheme.PASTEL:
            self.colors = ['#FFB3BA', '#BAFFC9', '#BAE1FF', '#FFFFBA', '#FFDFBA']
        elif self.color_scheme == ColorScheme.MONOCHROME:
            self.colors = ['#2C3E50', '#34495E', '#7F8C8D', '#95A5A6', '#BDC3C7']

        logger.debug(f"{self._log_prefix} 应用颜色方案: {self.color_scheme.value}")

    def get_matplotlib_config(self) -> Dict[str, Any]:
        """获取Matplotlib配置字典"""
        return {
            'figure_size': self.figure_size,
            'dpi': self.dpi,
            'background_color': self.background_color,
            'text_color': self.text_color,
            'font_family': self.font.family,
            'font_size': self.font.size,
            'line_width': self.line.width,
            'line_alpha': self.line.alpha,
            'marker_size': self.marker.size,
            'fill_alpha': self.fill.alpha,
            'grid_enabled': self.grid.enabled,
            'grid_lines': self.grid.lines,
            'grid_alpha': self.grid.alpha,
            'grid_color': self.grid.color,
            'colors': self.colors,
            'smart_text': self.smart_text_enabled,
            'text_margin': self.text_collision_margin,
            'text_offset': self.text_radial_offset
        }


# ============================================================================
# 字体管理系统
# ============================================================================

class ChineseFontManager:
    """
    中文字体管理器

    功能：
    1. 自动检测系统可用的中文字体
    2. 按优先级配置字体
    3. 提供字体回退机制
    4. 支持不同操作系统的字体路径
    """

    def __init__(self):
        self._log_prefix = "[ChineseFontManager]"

        # 按操作系统定义字体优先级
        self.font_priorities = {
            'Windows': [
                'Microsoft YaHei',
                'Microsoft YaHei UI',
                'SimHei',
                'SimSun',
                'KaiTi',
                'FangSong'
            ],
            'Darwin': [  # macOS
                'PingFang SC',
                'Hiragino Sans GB',
                'STHeiti',
                'Arial Unicode MS',
                'Heiti SC'
            ],
            'Linux': [
                'Noto Sans CJK SC',
                'WenQuanYi Micro Hei',
                'WenQuanYi Zen Hei',
                'Droid Sans Fallback',
                'AR PL UMing CN'
            ]
        }

        # 检测系统和可用字体
        self.system = platform.system()
        self.available_fonts = self._detect_available_fonts()
        self.best_font = self._select_best_font()

        logger.debug(f"{self._log_prefix} 字体管理器初始化完成，系统: {self.system}")

    def _detect_available_fonts(self) -> List[str]:
        """检测系统可用的中文字体"""
        try:
            # 获取系统所有字体
            all_fonts = [f.name for f in fm.fontManager.ttflist]

            # 获取当前系统的优先字体列表
            priority_fonts = self.font_priorities.get(self.system, self.font_priorities['Windows'])

            # 筛选可用的优先字体
            available = []
            for font in priority_fonts:
                if font in all_fonts:
                    available.append(font)

            logger.info(f"{self._log_prefix} 检测到可用中文字体: {available}")
            return available

        except Exception as e:
            logger.error(f"{self._log_prefix} 字体检测失败: {str(e)}")
            return []

    def _select_best_font(self) -> Optional[str]:
        """选择最佳字体"""
        if self.available_fonts:
            best = self.available_fonts[0]
            logger.info(f"{self._log_prefix} 选择最佳字体: {best}")
            return best
        else:
            logger.warning(f"{self._log_prefix} 未找到可用中文字体，将使用系统默认字体")
            return None

    def configure_matplotlib_fonts(self) -> bool:
        """配置Matplotlib中文字体支持"""
        try:
            if self.best_font:
                # 设置matplotlib的字体
                plt.rcParams['font.sans-serif'] = [self.best_font] + plt.rcParams['font.sans-serif']
                plt.rcParams['axes.unicode_minus'] = False

                logger.info(f"{self._log_prefix} Matplotlib字体配置完成: {plt.rcParams['font.sans-serif'][:3]}...")
                return True
            else:
                logger.warning(f"{self._log_prefix} 无可用中文字体，跳过配置")
                return False

        except Exception as e:
            logger.error(f"{self._log_prefix} Matplotlib字体配置失败: {str(e)}")
            return False

    def test_chinese_display(self) -> bool:
        """测试中文显示是否正常"""
        try:
            # 创建临时图形测试中文显示
            fig, ax = plt.subplots(figsize=(1, 1))
            ax.text(0.5, 0.5, '测试', fontsize=12)
            plt.close(fig)

            logger.info(f"{self._log_prefix} 中文显示测试通过")
            return True

        except Exception as e:
            logger.warning(f"{self._log_prefix} 中文显示测试失败: {str(e)}")
            return False

    def get_best_font(self) -> Optional[str]:
        """获取最佳字体"""
        return self.best_font

    def get_font_info(self) -> Dict[str, Any]:
        """获取字体信息"""
        return {
            'system': self.system,
            'best_font': self.best_font,
            'available_fonts': self.available_fonts,
            'font_priorities': self.font_priorities.get(self.system, [])
        }


# ============================================================================
# 智能文本渲染系统
# ============================================================================

@dataclass
class TextBox:
    """文本框数据类"""
    text: str
    x: float
    y: float
    width: float
    height: float
    angle: float
    priority: int = 0  # 优先级，数值越高越重要

    def get_bounds(self) -> Tuple[float, float, float, float]:
        """获取文本框边界 (left, right, bottom, top)"""
        return (
            self.x - self.width / 2,
            self.x + self.width / 2,
            self.y - self.height / 2,
            self.y + self.height / 2
        )

    def overlaps_with(self, other: 'TextBox', margin: float = 0.02) -> bool:
        """检查是否与另一个文本框重叠"""
        left1, right1, bottom1, top1 = self.get_bounds()
        left2, right2, bottom2, top2 = other.get_bounds()

        # 添加边距
        left1 -= margin
        right1 += margin
        bottom1 -= margin
        top1 += margin

        # 检查重叠
        return not (right1 < left2 or left1 > right2 or top1 < bottom2 or bottom1 > top2)


class SmartTextRenderer:
    """
    智能文本渲染器

    功能：
    1. 自动计算文本位置避免重叠
    2. 智能调整文本方向和对齐
    3. 支持优先级排序
    4. 提供多种布局算法
    """

    def __init__(self, ax, config: Dict[str, Any]):
        """
        初始化智能文本渲染器

        Args:
            ax: matplotlib轴对象
            config: 配置字典
        """
        self.ax = ax
        self.config = config
        self.text_boxes: List[TextBox] = []
        self.rendered_texts = []
        self._log_prefix = "[SmartTextRenderer]"

        # 配置参数
        self.collision_margin = config.get('text_margin', 0.02)
        self.radial_offset = config.get('text_offset', 0.1)
        self.max_iterations = 50

        logger.debug(f"{self._log_prefix} 智能文本渲染器初始化完成")

    def add_text(self, text: str, angle: float, radius: float, priority: int = 0):
        """
        添加文本到渲染队列

        Args:
            text: 文本内容
            angle: 角度（弧度）
            radius: 半径
            priority: 优先级
        """
        # 计算初始位置
        x = radius * np.cos(angle)
        y = radius * np.sin(angle)

        # 估算文本尺寸
        text_width, text_height = self._estimate_text_size(text)

        # 创建文本框
        text_box = TextBox(
            text=text,
            x=x,
            y=y,
            width=text_width,
            height=text_height,
            angle=angle,
            priority=priority
        )

        self.text_boxes.append(text_box)
        logger.debug(f"{self._log_prefix} 添加文本: '{text}' at ({x:.3f}, {y:.3f})")

    def _estimate_text_size(self, text: str) -> Tuple[float, float]:
        """估算文本尺寸"""
        try:
            # 使用matplotlib的文本渲染器估算尺寸
            temp_text = self.ax.text(0, 0, text,
                                   fontsize=self.config.get('font_size', 12),
                                   fontfamily=self.config.get('font_family', 'sans-serif'))

            # 获取文本边界框
            bbox = temp_text.get_window_extent(renderer=self.ax.figure.canvas.get_renderer())

            # 转换到数据坐标
            bbox_data = bbox.transformed(self.ax.transData.inverted())
            width = bbox_data.width
            height = bbox_data.height

            # 移除临时文本
            temp_text.remove()

            logger.debug(f"{self._log_prefix} 文本 '{text}' 尺寸: {width:.3f} x {height:.3f}")
            return width, height

        except Exception as e:
            logger.warning(f"{self._log_prefix} 文本尺寸估算失败: {str(e)}")
            # 使用默认估算
            char_width = 0.02
            char_height = 0.03
            return len(text) * char_width, char_height

    def clear_texts(self):
        """清除所有文本框"""
        self.text_boxes.clear()
        for text_obj in self.rendered_texts:
            if text_obj:
                text_obj.remove()
        self.rendered_texts.clear()
        logger.debug(f"{self._log_prefix} 清除所有文本框")

    def render_all_texts(self):
        """渲染所有文本"""
        try:
            # 清除之前的文本
            for text_obj in self.rendered_texts:
                if text_obj:
                    text_obj.remove()
            self.rendered_texts.clear()

            # 优化布局
            self._optimize_layout()

            # 渲染文本
            for text_box in self.text_boxes:
                text_kwargs = {
                    'fontsize': self.config.get('font_size', 12),
                    'fontfamily': self.config.get('font_family', 'sans-serif'),
                    'ha': 'center',
                    'va': 'center',
                    'color': self.config.get('text_color', '#000000')
                }

                # 检查是否为极坐标系统
                is_polar = hasattr(self.ax, 'projection') and self.ax.name == 'polar'

                if is_polar:
                    # 极坐标系统：使用角度和半径
                    angle = text_box.angle
                    radius = np.sqrt(text_box.x**2 + text_box.y**2)

                    # 确保角度和半径值有效
                    if np.isfinite(angle) and np.isfinite(radius) and radius > 0:
                        # 确保文本方向正确（不旋转）
                        text_kwargs['rotation'] = 0

                        # 创建文本对象
                        text_obj = self.ax.text(
                            angle, radius, text_box.text,
                            **text_kwargs
                        )
                    else:
                        # 如果极坐标值无效，回退到笛卡尔坐标
                        text_obj = self.ax.text(
                            text_box.x, text_box.y, text_box.text,
                            **text_kwargs
                        )
                else:
                    # 笛卡尔坐标系统：使用x, y坐标
                    text_obj = self.ax.text(
                        text_box.x, text_box.y, text_box.text,
                        **text_kwargs
                    )

                self.rendered_texts.append(text_obj)

            logger.info(f"{self._log_prefix} 渲染完成，共 {len(self.text_boxes)} 个文本")

        except Exception as e:
            logger.error(f"{self._log_prefix} 文本渲染失败: {str(e)}")

    def _optimize_layout(self):
        """优化文本布局以避免重叠"""
        if len(self.text_boxes) <= 1:
            return

        # 按优先级排序
        self.text_boxes.sort(key=lambda tb: tb.priority, reverse=True)

        # 迭代优化
        for iteration in range(self.max_iterations):
            collisions = self._detect_collisions()
            if not collisions:
                break

            # 解决碰撞
            self._resolve_collisions(collisions)

        logger.info(f"{self._log_prefix} 布局优化完成，迭代次数: {iteration}")

    def _detect_collisions(self) -> List[Tuple[int, int]]:
        """检测文本碰撞"""
        collisions = []
        for i in range(len(self.text_boxes)):
            for j in range(i + 1, len(self.text_boxes)):
                if self.text_boxes[i].overlaps_with(self.text_boxes[j], self.collision_margin):
                    collisions.append((i, j))

        logger.debug(f"{self._log_prefix} 检测到 {len(collisions)} 个碰撞")
        return collisions

    def _resolve_collisions(self, collisions: List[Tuple[int, int]]):
        """解决文本碰撞"""
        for i, j in collisions:
            # 优先级低的文本进行调整
            if self.text_boxes[i].priority >= self.text_boxes[j].priority:
                self._adjust_text_position(j)
            else:
                self._adjust_text_position(i)

    def _adjust_text_position(self, index: int):
        """调整文本位置"""
        text_box = self.text_boxes[index]

        # 计算径向偏移
        current_radius = np.sqrt(text_box.x**2 + text_box.y**2)
        new_radius = current_radius + self.radial_offset

        # 更新位置
        text_box.x = new_radius * np.cos(text_box.angle)
        text_box.y = new_radius * np.sin(text_box.angle)


# ============================================================================
# 数据处理器
# ============================================================================

class DataProcessor:
    """
    雷达图数据处理器

    功能：
    1. 数据验证和清理
    2. 角度计算和标准化
    3. 数值缩放和归一化
    4. 图形数据准备
    """

    def __init__(self):
        self._log_prefix = "[DataProcessor]"

    def process_radar_data(self, data: List[float], labels: List[str]) -> Tuple[List[float], List[float]]:
        """
        处理雷达图数据

        Args:
            data: 数据值列表
            labels: 标签列表

        Returns:
            Tuple[List[float], List[float]]: (处理后的数值, 角度列表)
        """
        try:
            # 1. 数据验证
            validated_data = self._validate_data(data, labels)

            # 2. 角度计算（从顶部开始，顺时针）
            n_dims = len(validated_data)
            angles = np.linspace(0, 2*np.pi, n_dims, endpoint=False)
            angles = angles - np.pi/2  # 调整起始位置到顶部（12点方向）

            # 3. 闭合图形（添加第一个点到末尾）
            values = list(validated_data) + [validated_data[0]]
            angles_closed = list(angles) + [angles[0]]

            logger.debug(f"{self._log_prefix} 数据处理完成: {n_dims}维, 角度范围: {np.degrees(angles).tolist()}")
            return values, angles_closed

        except Exception as e:
            logger.error(f"{self._log_prefix} 数据处理失败: {str(e)}")
            raise

    def _validate_data(self, data: List[float], labels: List[str]) -> List[float]:
        """验证和清理数据"""
        if len(data) != len(labels):
            raise ValueError(f"数据长度({len(data)})与标签长度({len(labels)})不匹配")

        if len(data) < 3:
            raise ValueError("雷达图至少需要3个维度的数据")

        # 处理无效值
        validated_data = []
        for i, value in enumerate(data):
            if value is None or not np.isfinite(value):
                logger.warning(f"{self._log_prefix} 数据点{i}无效，使用0替代")
                validated_data.append(0.0)
            else:
                validated_data.append(float(value))

        return validated_data

    def normalize_data(self, data: List[float], method: str = 'minmax') -> List[float]:
        """
        数据标准化

        Args:
            data: 原始数据
            method: 标准化方法 ('minmax', 'zscore', 'none')

        Returns:
            List[float]: 标准化后的数据
        """
        if method == 'none':
            return data

        data_array = np.array(data)

        if method == 'minmax':
            # Min-Max标准化到0-1范围
            min_val = np.min(data_array)
            max_val = np.max(data_array)
            if max_val > min_val:
                normalized = (data_array - min_val) / (max_val - min_val)
            else:
                normalized = np.ones_like(data_array) * 0.5
        elif method == 'zscore':
            # Z-score标准化
            mean_val = np.mean(data_array)
            std_val = np.std(data_array)
            if std_val > 0:
                normalized = (data_array - mean_val) / std_val
                # 转换到0-1范围
                normalized = (normalized - np.min(normalized)) / (np.max(normalized) - np.min(normalized))
            else:
                normalized = np.ones_like(data_array) * 0.5
        else:
            raise ValueError(f"不支持的标准化方法: {method}")

        logger.debug(f"{self._log_prefix} 数据标准化完成，方法: {method}")
        return normalized.tolist()


# ============================================================================
# Matplotlib雷达图实现
# ============================================================================

class MatplotlibRadarChart(BaseChart):
    """
    基于Matplotlib的专业雷达图实现

    特性：
    - 完美的中文字体支持
    - 智能文本重叠避免
    - 自动文本重叠避免
    - 专业的极坐标系统
    - 丰富的样式控制
    """

    def __init__(self, theme: ChartTheme = ChartTheme.LIGHT,
                 color_scheme: ColorScheme = ColorScheme.DEFAULT,
                 config: Optional[RadarChartConfig] = None,
                 use_smart_text: bool = True, **kwargs):
        """
        初始化Matplotlib雷达图

        Args:
            theme: 图表主题
            color_scheme: 颜色方案
            config: 自定义配置对象
            use_smart_text: 是否使用智能文本渲染（False则使用传统方法）
            **kwargs: 传递给BaseChart的参数
        """
        super().__init__(chart_type=ChartType.RADAR, **kwargs)

        # Matplotlib相关组件
        self.figure = None
        self.ax = None
        self.canvas = None

        # 数据处理器
        self.data_processor = DataProcessor()

        # 字体管理器
        self.font_manager = ChineseFontManager()

        # 智能文本渲染器（将在_create_polar_axes后初始化）
        self.text_renderer = None

        # 数据存储
        self._data_items = {}
        self._categories = []
        self._max_value = 100

        # 配置管理器
        if config is not None:
            self.chart_config = config
        else:
            self.chart_config = RadarChartConfig(theme=theme, color_scheme=color_scheme)

        # 获取Matplotlib配置
        self.config = self.chart_config.get_matplotlib_config()

        # 设置文本渲染方式
        self.config['smart_text'] = use_smart_text

        # 确保字体配置一致
        if self.font_manager.get_best_font():
            self.config['font_family'] = self.font_manager.get_best_font()

        # 设置Matplotlib环境
        self._setup_matplotlib_environment()

        logger.debug(f"{self._log_prefix} MatplotlibRadarChart创建完成")

    def _initialize_specific(self):
        """实现BaseChart的抽象方法"""
        self._initialize_chart()

    def _setup_matplotlib_environment(self):
        """配置Matplotlib环境"""
        try:
            # 使用字体管理器配置中文字体支持
            font_config_success = self.font_manager.configure_matplotlib_fonts()

            if not font_config_success:
                logger.warning(f"{self._log_prefix} 字体配置失败，可能影响中文显示")

            # 配置图形参数
            plt.rcParams['figure.figsize'] = self.config['figure_size']
            plt.rcParams['figure.dpi'] = self.config['dpi']

            # 测试中文显示
            if self.font_manager.test_chinese_display():
                logger.info(f"{self._log_prefix} 中文显示测试通过")
            else:
                logger.warning(f"{self._log_prefix} 中文显示测试失败")

            # 获取字体信息
            font_info = self.font_manager.get_font_info()
            logger.info(f"{self._log_prefix} 字体配置: {font_info['best_font']}, "
                       f"可用字体数: {len(font_info['available_fonts'])}")

        except Exception as e:
            logger.error(f"{self._log_prefix} Matplotlib环境配置失败: {str(e)}")
            raise

    def _initialize_chart(self):
        """初始化图表"""
        try:
            # 创建图形和画布
            self.figure = Figure(
                figsize=self.config['figure_size'],
                dpi=self.config['dpi'],
                facecolor=self.config['background_color']
            )

            self.canvas = FigureCanvas(self.figure)

            # 创建极坐标轴
            self._create_polar_axes()

            logger.debug(f"{self._log_prefix} 图表初始化完成")

        except Exception as e:
            logger.error(f"{self._log_prefix} 图表初始化失败: {str(e)}")
            raise

    def _create_polar_axes(self):
        """创建极坐标轴"""
        try:
            # 创建极坐标子图
            self.ax = self.figure.add_subplot(111, projection='polar')

            # 设置起始角度（顶部开始）
            self.ax.set_theta_zero_location('N')
            self.ax.set_theta_direction(-1)  # 顺时针

            # 初始化智能文本渲染器
            if self.config.get('smart_text', True):
                self.text_renderer = SmartTextRenderer(self.ax, self.config)
                logger.debug(f"{self._log_prefix} 智能文本渲染器初始化完成")

            # 应用样式
            self._apply_polar_styling()

            logger.debug(f"{self._log_prefix} 极坐标轴创建完成")

        except Exception as e:
            logger.error(f"{self._log_prefix} 极坐标轴创建失败: {str(e)}")
            raise

    def _apply_polar_styling(self):
        """应用极坐标样式"""
        try:
            # 设置背景色
            self.ax.set_facecolor(self.config['background_color'])

            # 配置网格
            if self.config['grid_enabled']:
                self.ax.grid(True, alpha=self.config['grid_alpha'],
                           color=self.config['grid_color'],
                           linewidth=0.5)

            # 隐藏径向标签（数值标签）
            self.ax.set_yticklabels([])

            # 设置径向范围
            self.ax.set_ylim(0, 1)

            logger.debug(f"{self._log_prefix} 极坐标样式应用完成")

        except Exception as e:
            logger.error(f"{self._log_prefix} 极坐标样式应用失败: {str(e)}")

    def add_data(self, name: str, data: Union[List[float], Dict[str, Any]],
                 categories: Optional[List[str]] = None, **kwargs):
        """
        添加数据到雷达图

        Args:
            name: 数据系列名称
            data: 数据值或数据字典
            categories: 类别标签
            **kwargs: 额外参数（color, fill_alpha等）
        """
        try:
            # 确保图表已初始化
            if self.ax is None:
                self._initialize_chart()

            # 处理数据格式
            if isinstance(data, dict):
                values = data.get('values', [])
                labels = data.get('labels', categories or [])
            else:
                values = data
                labels = categories or []

            # 处理数据
            processed_values, angles = self.data_processor.process_radar_data(values, labels)

            # 设置轴标签（如果提供）
            if labels:
                self._set_axis_labels(labels, angles[:-1])  # 去掉闭合点的角度

            # 获取颜色
            color = kwargs.get('color', self.config['colors'][len(self._data_items) % len(self.config['colors'])])

            # 转换颜色格式（从0-255到0-1）
            if isinstance(color, (tuple, list)) and len(color) >= 3:
                if all(isinstance(c, int) and c > 1 for c in color[:3]):
                    # 转换RGB(255)到RGB(1.0)
                    color = tuple(c / 255.0 for c in color[:3])

            # 绘制数据线
            line = self.ax.plot(angles, processed_values,
                              color=color,
                              linewidth=self.config['line_width'],
                              alpha=self.config['line_alpha'],
                              label=name,
                              marker='o',
                              markersize=self.config['marker_size'])

            # 填充区域
            fill_alpha = kwargs.get('fill_alpha', self.config['fill_alpha'])
            if fill_alpha > 0:
                # 确保alpha值在0-1范围内
                if fill_alpha > 1:
                    fill_alpha = fill_alpha / 255.0  # 转换从0-255到0-1
                self.ax.fill(angles, processed_values,
                           color=color, alpha=fill_alpha)

            # 存储数据项
            self._data_items[name] = {
                'values': processed_values,
                'angles': angles,
                'color': color,
                'line': line
            }

            # 更新显示
            self._update_display()

            logger.debug(f"{self._log_prefix} 添加数据成功: {name}")

        except Exception as e:
            logger.error(f"{self._log_prefix} 添加数据失败: {str(e)}")
            raise

    def _set_axis_labels(self, labels: List[str], angles: List[float]):
        """设置轴标签"""
        try:
            if self.config.get('smart_text', True) and self.text_renderer:
                # 使用智能文本渲染
                self._set_smart_axis_labels(labels, angles)
            else:
                # 使用传统方法
                self._set_traditional_axis_labels(labels, angles)

        except Exception as e:
            logger.error(f"{self._log_prefix} 轴标签设置失败: {str(e)}")

    def _set_smart_axis_labels(self, labels: List[str], angles: List[float]):
        """使用智能文本渲染设置轴标签"""
        try:
            # 清除之前的文本
            self.text_renderer.clear_texts()

            # 添加标签文本
            for i, (label, angle) in enumerate(zip(labels, angles)):
                # 计算文本位置（稍微向外偏移）
                radius = 1.0 + self.config.get('text_offset', 0.1)
                self.text_renderer.add_text(label, angle, radius, priority=10)

            # 渲染所有文本
            self.text_renderer.render_all_texts()

            logger.debug(f"{self._log_prefix} 智能轴标签设置完成")

        except Exception as e:
            logger.error(f"{self._log_prefix} 智能轴标签设置失败: {str(e)}")

    def _set_traditional_axis_labels(self, labels: List[str], angles: List[float]):
        """使用传统方法设置轴标签"""
        try:
            # 设置角度标签
            self.ax.set_thetagrids(np.degrees(angles), labels,
                                  fontsize=self.config['font_size'],
                                  fontfamily=self.config['font_family'])

            logger.debug(f"{self._log_prefix} 传统轴标签设置完成")

        except Exception as e:
            logger.error(f"{self._log_prefix} 传统轴标签设置失败: {str(e)}")

    def _update_display(self):
        """更新显示"""
        try:
            # 刷新画布
            if self.canvas:
                self.canvas.draw()

        except Exception as e:
            logger.error(f"{self._log_prefix} 显示更新失败: {str(e)}")

    def get_widget(self):
        """获取Qt widget"""
        if self.canvas is None:
            self._initialize_chart()
        return self.canvas

    def set_categories(self, categories: List[str]):
        """设置类别标签"""
        self._categories = categories
        logger.debug(f"{self._log_prefix} 设置类别: {categories}")

    def set_max_value(self, max_value: float, auto_scale: bool = True):
        """设置最大值"""
        self._max_value = max_value
        if self.ax:
            self.ax.set_ylim(0, max_value)

    def update_data(self, name: str, data: List[float]):
        """更新数据"""
        if name in self._data_items:
            # 重新处理数据
            processed_values, angles = self.data_processor.process_radar_data(data, self._categories)

            # 更新线条数据
            line = self._data_items[name]['line'][0]
            line.set_data(angles, processed_values)

            # 更新存储的数据
            self._data_items[name]['values'] = processed_values
            self._data_items[name]['angles'] = angles

            self._update_display()

    def clear_data(self):
        """清除所有数据"""
        if self.ax:
            self.ax.clear()
            self._create_polar_axes()
        self._data_items.clear()

    def get_implementation_info(self) -> Dict[str, Any]:
        """获取实现信息"""
        return {
            'implementation': 'matplotlib',
            'features': {
                'smart_text_rendering': self.config.get('smart_text', True),
                'advanced_styling': True,
                'theme_support': True,
                'config_persistence': True,
                'series_comparison': True,
                'chinese_font_support': True
            }
        }


class RadarPlotItem(pg.GraphicsObject):
    """雷达图项目"""
    
    def __init__(
        self, 
        values: List[float], 
        labels: List[str], 
        scale: float = 1.0,
        color: Tuple[int, int, int] = (0, 0, 255),
        fill_color: Optional[Tuple[int, int, int, int]] = None,
        line_width: float = 2.0,
        symbol: str = None,
        symbol_size: int = 10,
        name: str = ""
    ):
        """
        初始化雷达图项目
        
        Args:
            values: 数据值列表
            labels: 对应的标签列表
            scale: 缩放比例
            color: 线条颜色
            fill_color: 填充颜色，默认为透明
            line_width: 线条宽度
            symbol: 节点符号，如 'o', 't', '+', 'd', 's' 等
            symbol_size: 节点大小
            name: 名称
        """
        super().__init__()
        
        self.values = values
        self.labels = labels
        self.scale = scale
        self.color = color
        self.line_width = line_width
        self.symbol = symbol
        self.symbol_size = symbol_size
        self.name = name
        
        # 设置填充颜色
        if fill_color is None:
            # 默认使用半透明的线条颜色
            self.fill_color = (*color, 50)  # 透明度50
        else:
            self.fill_color = fill_color
        
        # 创建画笔和画刷
        self.pen = pg.mkPen(color=color, width=line_width)
        self.fill_brush = pg.mkBrush(color=self.fill_color)
        
        # 计算路径和点
        self.polygon_path = None
        self.points = []
        self._calculate_polygon()
    
    def _calculate_polygon(self):
        """计算多边形路径"""
        # 获取维度数量
        n_dims = len(self.values)
        if n_dims < 3:
            # 至少需要3个维度才能绘制雷达图
            raise ValueError("雷达图至少需要3个维度")

        # 计算每个轴的角度 - 从顶部开始（12点方向），顺时针排列
        angles = [i * 2 * np.pi / n_dims - np.pi / 2 for i in range(n_dims)]
        
        # 计算多边形的顶点坐标
        self.points = []
        for i, value in enumerate(self.values):
            # 标准化到0-1范围
            normalized_value = value * self.scale
            
            # 计算坐标
            x = normalized_value * np.cos(angles[i])
            y = normalized_value * np.sin(angles[i])
            
            self.points.append((x, y))
        
        # 创建多边形路径
        self.polygon_path = QtGui.QPainterPath()
        self.polygon_path.moveTo(self.points[0][0], self.points[0][1])
        for x, y in self.points[1:]:
            self.polygon_path.lineTo(x, y)
        self.polygon_path.closeSubpath()
    
    def boundingRect(self):
        """获取包围盒"""
        # 确保多边形路径已计算
        if self.polygon_path is None:
            self._calculate_polygon()
        
        # 获取路径的包围盒并添加一些边距
        rect = self.polygon_path.boundingRect()
        padding = max(self.line_width, self.symbol_size if self.symbol else 0) + 2
        return rect.adjusted(-padding, -padding, padding, padding)
    
    def paint(self, painter, option, widget=None):
        """绘制雷达图"""
        # 确保多边形路径已计算
        if self.polygon_path is None:
            self._calculate_polygon()
        
        # 设置抗锯齿
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        
        # 绘制填充区域
        painter.setBrush(self.fill_brush)
        painter.setPen(QtCore.Qt.NoPen)
        painter.drawPath(self.polygon_path)
        
        # 绘制轮廓线
        painter.setBrush(QtCore.Qt.NoBrush)
        painter.setPen(self.pen)
        painter.drawPath(self.polygon_path)
        
        # 绘制节点符号
        if self.symbol:
            sym_size = self.symbol_size
            half_size = sym_size / 2
            
            # 为不同的符号创建不同的路径
            sym_path = QtGui.QPainterPath()
            
            if self.symbol == 'o':  # 圆形
                sym_path.addEllipse(QtCore.QRectF(-half_size, -half_size, sym_size, sym_size))
            elif self.symbol == 's':  # 正方形
                sym_path.addRect(QtCore.QRectF(-half_size, -half_size, sym_size, sym_size))
            elif self.symbol == 'd':  # 菱形
                sym_path.moveTo(0, -half_size)
                sym_path.lineTo(half_size, 0)
                sym_path.lineTo(0, half_size)
                sym_path.lineTo(-half_size, 0)
                sym_path.closeSubpath()
            elif self.symbol == '+':  # 加号
                sym_path.moveTo(-half_size, 0)
                sym_path.lineTo(half_size, 0)
                sym_path.moveTo(0, -half_size)
                sym_path.lineTo(0, half_size)
            elif self.symbol == 't':  # 三角形
                sym_path.moveTo(0, -half_size)
                sym_path.lineTo(half_size, half_size)
                sym_path.lineTo(-half_size, half_size)
                sym_path.closeSubpath()
            else:  # 默认圆形
                sym_path.addEllipse(QtCore.QRectF(-half_size, -half_size, sym_size, sym_size))
            
            # 设置节点画刷
            painter.setBrush(pg.mkBrush(self.color))
            
            # 在每个顶点绘制符号
            for x, y in self.points:
                painter.save()
                painter.translate(x, y)
                painter.drawPath(sym_path)
                painter.restore()


class RadarAxisItem(pg.GraphicsObject):
    """雷达图轴项目"""
    
    def __init__(
        self, 
        labels: List[str], 
        max_value: float = 1.0,
        n_circles: int = 4,
        axis_color: Tuple[int, int, int] = (100, 100, 100),
        grid_color: Tuple[int, int, int] = (200, 200, 200),
        text_color: Tuple[int, int, int] = (0, 0, 0),
        label_offset: float = 0.1,
        grid_line_width: float = 1.0,
        axis_line_width: float = 1.5
    ):
        """
        初始化雷达图轴
        
        Args:
            labels: 各轴的标签列表
            max_value: 最大值
            n_circles: 同心圆数量
            axis_color: 轴线颜色
            grid_color: 网格线颜色
            text_color: 文本颜色
            label_offset: 标签偏移量
            grid_line_width: 网格线宽度
            axis_line_width: 轴线宽度
        """
        super().__init__()
        
        self.labels = labels
        self.max_value = max_value
        self.n_circles = n_circles
        self.label_offset = label_offset
        
        # 创建画笔
        self.axis_pen = pg.mkPen(color=axis_color, width=axis_line_width)
        self.grid_pen = pg.mkPen(color=grid_color, width=grid_line_width, style=QtCore.Qt.DashLine)
        self.text_color = text_color
        
        # 计算轴线和网格线
        self.axis_lines = []
        self.grid_circles = []
        self._calculate_axes()
    
    def _calculate_axes(self):
        """计算轴线和网格线"""
        # 获取维度数量
        n_dims = len(self.labels)
        if n_dims < 3:
            # 至少需要3个维度才能绘制雷达图
            raise ValueError("雷达图至少需要3个维度")

        # 计算每个轴的角度 - 从顶部开始（12点方向），顺时针排列
        angles = [i * 2 * np.pi / n_dims - np.pi / 2 for i in range(n_dims)]
        
        # 计算轴线
        self.axis_lines = []
        for angle in angles:
            # 轴延伸到稍微超过最大值的位置，以便绘制标签
            x = (self.max_value + self.label_offset) * np.cos(angle)
            y = (self.max_value + self.label_offset) * np.sin(angle)
            self.axis_lines.append(((0, 0), (x, y)))
        
        # 计算同心圆
        self.grid_circles = []
        for i in range(1, self.n_circles + 1):
            radius = self.max_value * i / self.n_circles
            self.grid_circles.append(radius)

    def _get_text_position_and_alignment(self, angle, x, y, text_width, text_height, margin=5):
        """
        根据角度计算文本的最佳位置和对齐方式

        Args:
            angle: 角度（弧度）
            x, y: 基准位置
            text_width, text_height: 文本尺寸
            margin: 边距

        Returns:
            tuple: (text_x, text_y, alignment_flags)
        """
        # 将角度标准化到 [0, 2π) 范围
        angle = angle % (2 * np.pi)

        if angle >= 7*np.pi/4 or angle < np.pi/4:  # 右侧 (315° - 45°)
            text_x = x + margin
            text_y = y - text_height / 2
            flags = QtCore.Qt.AlignLeft | QtCore.Qt.AlignVCenter
        elif angle >= np.pi/4 and angle < 3*np.pi/4:  # 下方 (45° - 135°)
            text_x = x - text_width / 2
            text_y = y + margin
            flags = QtCore.Qt.AlignHCenter | QtCore.Qt.AlignTop
        elif angle >= 3*np.pi/4 and angle < 5*np.pi/4:  # 左侧 (135° - 225°)
            text_x = x - text_width - margin
            text_y = y - text_height / 2
            flags = QtCore.Qt.AlignRight | QtCore.Qt.AlignVCenter
        else:  # 上方 (225° - 315°)
            text_x = x - text_width / 2
            text_y = y - text_height - margin
            flags = QtCore.Qt.AlignHCenter | QtCore.Qt.AlignBottom

        return text_x, text_y, flags
    
    def boundingRect(self):
        """获取包围盒"""
        # 计算包含所有元素的矩形
        max_extent = self.max_value + self.label_offset + 0.2  # 添加标签的空间
        return QtCore.QRectF(-max_extent, -max_extent, 2 * max_extent, 2 * max_extent)
    
    def paint(self, painter, option, widget=None):
        """绘制雷达图轴"""
        # 设置抗锯齿
        painter.setRenderHint(QtGui.QPainter.Antialiasing)
        
        # 绘制同心圆网格
        painter.setPen(self.grid_pen)
        for radius in self.grid_circles:
            painter.drawEllipse(QtCore.QPointF(0, 0), radius, radius)
        
        # 绘制轴线
        painter.setPen(self.axis_pen)
        for start, end in self.axis_lines:
            painter.drawLine(QtCore.QPointF(*start), QtCore.QPointF(*end))
        
        # 绘制标签
        n_dims = len(self.labels)
        angles = [i * 2 * np.pi / n_dims - np.pi / 2 for i in range(n_dims)]  # 从顶部开始

        # 设置字体 - 支持中文显示
        font = painter.font()
        font.setFamily("Microsoft YaHei, SimHei, Arial Unicode MS, sans-serif")  # 优先使用中文字体
        font.setPointSize(10)  # 设置合适的字体大小
        font.setBold(True)
        painter.setFont(font)

        # 获取字体度量信息
        font_metrics = QtGui.QFontMetrics(font)

        for i, label in enumerate(self.labels):
            angle = angles[i]

            # 计算标签位置
            label_radius = self.max_value + self.label_offset
            x = label_radius * np.cos(angle)
            y = label_radius * np.sin(angle)

            # 动态计算文本尺寸
            text_width = font_metrics.horizontalAdvance(label)
            text_height = font_metrics.height()

            # 使用辅助方法计算最佳文本位置和对齐方式
            text_x, text_y, flags = self._get_text_position_and_alignment(
                angle, x, y, text_width, text_height, margin=8
            )

            # 创建精确的文本显示矩形
            text_rect = QtCore.QRectF(text_x, text_y, text_width + 10, text_height + 4)

            # 设置文本颜色
            painter.setPen(pg.mkPen(self.text_color))

            # 绘制标签文本
            painter.drawText(text_rect, flags, label)
        
        # 绘制数值标签
        painter.setPen(pg.mkPen(self.text_color))
        for i, radius in enumerate(self.grid_circles):
            value = self.max_value * (i + 1) / self.n_circles
            value_str = str(round(value, 2))
            painter.drawText(QtCore.QPointF(radius + 5, 0), value_str)


class RadarChart(BaseChart):
    """
    雷达图类 - 新版本集成实现

    自动选择最佳实现（Matplotlib优先），解决文本重叠和中文显示问题。
    保持与原有PyQtGraph接口的完全兼容性。
    """

    def __init__(
        self,
        title: str = "雷达图",
        x_label: str = "",
        y_label: str = "",
        prefer_matplotlib: bool = True,
        use_smart_text: bool = False,  # 默认使用传统文本渲染避免方向问题
        **kwargs
    ):
        """
        初始化雷达图

        Args:
            title: 图表标题
            x_label: X轴标签（兼容性参数）
            y_label: Y轴标签（兼容性参数）
            prefer_matplotlib: 是否优先使用Matplotlib实现
            use_smart_text: 是否使用智能文本渲染（False使用传统方法，避免文本方向问题）
            **kwargs: 额外参数
        """
        super().__init__(chart_type=ChartType.RADAR, **kwargs)
        self._log_prefix = "[RadarChart]"

        # 尝试使用Matplotlib实现
        if MATPLOTLIB_AVAILABLE and prefer_matplotlib:
            try:
                self._implementation = MatplotlibRadarChart(
                    theme=ChartTheme.LIGHT,
                    color_scheme=ColorScheme.DEFAULT,
                    use_smart_text=use_smart_text
                )
                self._use_matplotlib = True
                logger.info(f"{self._log_prefix} 使用Matplotlib实现")
                if not use_smart_text:
                    logger.info(f"{self._log_prefix} 使用传统文本渲染避免方向问题")

            except Exception as e:
                logger.warning(f"{self._log_prefix} Matplotlib实现创建失败: {e}")
                self._use_matplotlib = False
                self._create_fallback_implementation(title, x_label, y_label)
        else:
            logger.info(f"{self._log_prefix} 使用PyQtGraph回退实现")
            self._use_matplotlib = False
            self._create_fallback_implementation(title, x_label, y_label)

        # 兼容性属性
        self._radar_items = {}  # 存储雷达图项目
        self._axis_item = None  # 雷达图轴
        self._categories = []  # 类别名称
        self._max_value = 1.0  # 最大值
        self._auto_scale = True  # 自动调整比例
        self._fill_alpha = 50  # 填充透明度
        self._n_circles = 4  # 同心圆数量
        self._legend_visible = True  # 图例是否可见

        logger.debug(f"{self._log_prefix} 雷达图初始化完成")

    def _create_fallback_implementation(self, title: str, x_label: str, y_label: str):
        """创建回退实现"""
        if PYQTGRAPH_AVAILABLE:
            # 使用原有PyQtGraph实现的简化版本
            self._implementation = None  # 将在需要时创建
            logger.info(f"{self._log_prefix} 准备使用PyQtGraph回退实现")
        else:
            raise RuntimeError("没有可用的雷达图实现")
    
    def _initialize_specific(self):
        """实现图表特定的初始化"""
        if self._use_matplotlib:
            # 使用集成适配器的初始化
            if hasattr(self._implementation, '_initialize_specific'):
                self._implementation._initialize_specific()
            elif hasattr(self._implementation, 'initialize'):
                self._implementation.initialize()
        else:
            # 回退到原有实现
            if hasattr(self, '_plot_item') and self._plot_item:
                self._plot_item.getAxis('bottom').hide()
                self._plot_item.getAxis('left').hide()
                self._plot_item.setAspectLocked(True)
                if hasattr(self._plot_item, 'addLegend'):
                    self._plot_item.addLegend()
    
    def get_widget(self):
        """获取图表控件"""
        if self._use_matplotlib:
            return self._implementation.get_widget()
        else:
            return super().get_widget() if hasattr(super(), 'get_widget') else self._plot_widget

    def set_categories(self, categories: List[str]):
        """
        设置类别标签

        Args:
            categories: 类别名称列表
        """
        self._categories = categories

        if self._use_matplotlib:
            self._implementation.set_categories(categories)
        else:
            # 原有实现逻辑
            if self._initialized:
                self._update_axis()

    def set_max_value(self, max_value: float, auto_scale: bool = False):
        """
        设置最大值和是否自动缩放

        Args:
            max_value: 最大值
            auto_scale: 是否自动缩放
        """
        self._max_value = max_value
        self._auto_scale = auto_scale

        if self._use_matplotlib:
            self._implementation.set_max_value(max_value, auto_scale)
        else:
            # 原有实现逻辑
            if self._initialized:
                self._update_axis()
                if not auto_scale:
                    for radar_item in self._radar_items.values():
                        radar_item.scale = 1.0
    
    def _update_axis(self):
        """更新雷达图轴"""
        if not self._categories:
            return
        
        # 移除旧的轴
        if self._axis_item is not None:
            self._plot_item.removeItem(self._axis_item)
        
        # 创建新的轴
        self._axis_item = RadarAxisItem(
            labels=self._categories,
            max_value=self._max_value,
            n_circles=self._n_circles
        )
        
        # 添加到绘图项
        self._plot_item.addItem(self._axis_item)

    def _validate_data(self, data, categories=None):
        """
        验证数据的有效性

        Args:
            data: 数据内容
            categories: 类别标签列表

        Returns:
            tuple: (values, categories) 验证后的数据

        Raises:
            ValueError: 数据无效时抛出异常
        """
        values = []

        if isinstance(data, dict):
            if not data:
                raise ValueError("数据字典不能为空")

            # 如果没有提供类别，从字典键中提取
            if categories is None:
                categories = list(data.keys())

            # 按照类别顺序提取值
            values = []
            for cat in categories:
                if cat not in data:
                    raise ValueError(f"数据中缺少类别: {cat}")
                value = data[cat]
                if not isinstance(value, (int, float, np.number)):
                    raise ValueError(f"类别 '{cat}' 的值必须是数字，当前类型: {type(value)}")
                if value < 0:
                    raise ValueError(f"类别 '{cat}' 的值不能为负数: {value}")
                values.append(float(value))

        elif isinstance(data, (list, tuple, np.ndarray)):
            if len(data) == 0:
                raise ValueError("数据列表不能为空")

            values = []
            for i, value in enumerate(data):
                if not isinstance(value, (int, float, np.number)):
                    raise ValueError(f"索引 {i} 的值必须是数字，当前类型: {type(value)}")
                if value < 0:
                    raise ValueError(f"索引 {i} 的值不能为负数: {value}")
                values.append(float(value))
        else:
            raise ValueError(f"不支持的数据类型: {type(data)}")

        # 检查维度数量
        if len(values) < 3:
            raise ValueError(f"雷达图至少需要3个维度，当前只有 {len(values)} 个")

        return values, categories

    def _parse_style_params(self, **kwargs):
        """
        解析样式参数

        Args:
            **kwargs: 样式参数

        Returns:
            dict: 解析后的样式参数
        """
        # 预定义颜色列表
        default_colors = [
            (255, 0, 0),      # 红色
            (0, 0, 255),      # 蓝色
            (0, 255, 0),      # 绿色
            (255, 165, 0),    # 橙色
            (128, 0, 128),    # 紫色
            (255, 192, 203),  # 粉红色
            (165, 42, 42),    # 棕色
            (0, 255, 255),    # 青色
        ]

        style = {
            'name': kwargs.get('name', ''),
            'color': kwargs.get('color', default_colors[len(self._data_items) % len(default_colors)]),
            'fill_color': kwargs.get('fill_color', None),
            'line_width': kwargs.get('line_width', 2.0),
            'symbol': kwargs.get('symbol', 'o'),
            'symbol_size': kwargs.get('symbol_size', 10),
            'fill_alpha': kwargs.get('fill_alpha', self._fill_alpha),
        }

        # 处理填充颜色
        if style['fill_color'] is None:
            style['fill_color'] = (*style['color'][:3], style['fill_alpha'])

        return style

    def add_data(
        self,
        data_id: str,
        data: Union[List[float], Dict[str, float]],
        **kwargs
    ):
        """
        添加数据到雷达图

        Args:
            data_id: 数据标识符
            data: 数据内容，可以是数据列表或字典 {类别: 数值}
            **kwargs: 额外配置参数，包括：
                - name: 数据系列名称（显示在图例中）
                - color: 线条颜色，格式为 (R,G,B) 或 '#RRGGBB'
                - fill_color: 填充颜色，格式为 (R,G,B,A) 或 '#RRGGBB'
                - line_width: 线条宽度，默认为2
                - symbol: 节点符号，如 'o', 't', '+', 'd', 's' 等
                - symbol_size: 节点大小，默认为10
                - categories: 类别标签列表（当data为列表时使用）
                - max_value: 设置最大值
                - auto_scale: 是否自动缩放数据
                - fill_alpha: 填充透明度，0-255
        """
        try:
            if self._use_matplotlib:
                # 使用集成适配器
                self._implementation.add_data(data_id, data, **kwargs)

                # 更新兼容性属性
                if isinstance(data, list):
                    categories = kwargs.get('categories', self._categories)
                    if categories:
                        self._categories = categories
                elif isinstance(data, dict) and 'values' not in data:
                    self._categories = list(data.keys())

                logger.debug(f"{self._log_prefix} 添加数据成功 (集成): {data_id}")

            else:
                # 使用原有实现逻辑
                if not self._initialized:
                    self.initialize()

                # 解析和验证数据
                categories = kwargs.get('categories', None)
                values, categories = self._validate_data(data, categories)

                # 设置类别标签（如果还没有设置）
                if not self._categories and categories:
                    self.set_categories(categories)
                elif self._categories and len(values) != len(self._categories):
                    raise ValueError(f"数据长度 ({len(values)}) 与类别数量 ({len(self._categories)}) 不匹配")

                # 解析样式参数
                style = self._parse_style_params(**kwargs)
                if not style['name']:
                    style['name'] = data_id

                # 处理最大值和自动缩放
                max_value = kwargs.get('max_value', None)
                auto_scale = kwargs.get('auto_scale', self._auto_scale)

                if max_value is not None:
                    self.set_max_value(max_value, auto_scale)

                # 计算缩放比例
                scale = 1.0
                if auto_scale and values:
                    max_data = max(max(values), 0.001)  # 避免除以零
                    scale = self._max_value / max_data

                # 创建雷达图项
                radar_item = RadarPlotItem(
                    values=values,
                    labels=self._categories,
                    scale=scale,
                    color=style['color'],
                    fill_color=style['fill_color'],
                    line_width=style['line_width'],
                    symbol=style['symbol'],
                    symbol_size=style['symbol_size'],
                    name=style['name']
                )

                # 添加到绘图项
                if hasattr(self, '_plot_item') and self._plot_item:
                    self._plot_item.addItem(radar_item)

                # 存储雷达图项
                self._radar_items[data_id] = radar_item
                if hasattr(self, '_data_items'):
                    self._data_items[data_id] = radar_item

                logger.debug(f"{self._log_prefix} 添加数据成功 (原生): {data_id}, 维度: {len(values)}")

        except Exception as e:
            logger.error(f"{self._log_prefix} 添加数据失败: {str(e)}")
            raise
    
    def update_data(
        self,
        data_id: str,
        data: Union[List[float], Dict[str, float]],
        **kwargs
    ):
        """
        更新雷达图中的数据

        Args:
            data_id: 数据标识符
            data: 新的数据内容
            **kwargs: 额外的样式参数，与add_data相同
        """
        try:
            if self._use_matplotlib:
                # 使用集成适配器
                self._implementation.update_data(data_id, data, **kwargs)
                logger.debug(f"{self._log_prefix} 更新数据成功 (集成): {data_id}")
            else:
                # 使用原有实现逻辑
                if not self._initialized or data_id not in getattr(self, '_data_items', {}):
                    # 如果数据不存在，则添加新数据
                    self.add_data(data_id, data, **kwargs)
                    return

                # 移除旧的雷达图项
                if data_id in self._radar_items:
                    radar_item = self._radar_items[data_id]
                    if hasattr(self, '_plot_item') and self._plot_item:
                        self._plot_item.removeItem(radar_item)

                # 添加新的雷达图项
                self.add_data(data_id, data, **kwargs)
                logger.debug(f"{self._log_prefix} 更新数据成功 (原生): {data_id}")

        except Exception as e:
            logger.error(f"{self._log_prefix} 更新数据失败: {str(e)}")
            raise
    
    def set_fill_alpha(self, data_id: str, alpha: int):
        """
        设置填充透明度
        
        Args:
            data_id: 数据标识符
            alpha: 透明度，0-255
        """
        if not self._initialized or data_id not in self._radar_items:
            return
        
        radar_item = self._radar_items[data_id]
        
        # 创建新的填充颜色
        fill_color = (*radar_item.color[:3], alpha)
        fill_brush = pg.mkBrush(color=fill_color)
        
        # 更新填充画刷
        radar_item.fill_color = fill_color
        radar_item.fill_brush = fill_brush
        
        # 重新绘制
        radar_item.update()
    
    def set_line_width(self, data_id: str, width: float):
        """
        设置线条宽度
        
        Args:
            data_id: 数据标识符
            width: 线条宽度
        """
        if not self._initialized or data_id not in self._radar_items:
            return
        
        radar_item = self._radar_items[data_id]
        
        # 更新线条宽度
        radar_item.line_width = width
        radar_item.pen = pg.mkPen(color=radar_item.color, width=width)
        
        # 重新绘制
        radar_item.update()
    
    def set_symbol(self, data_id: str, symbol: str, size: int = None):
        """
        设置节点符号
        
        Args:
            data_id: 数据标识符
            symbol: 符号类型，如 'o', 't', '+', 'd', 's' 等
            size: 符号大小
        """
        if not self._initialized or data_id not in self._radar_items:
            return
        
        radar_item = self._radar_items[data_id]
        
        # 更新符号
        radar_item.symbol = symbol
        
        # 更新大小（如果提供）
        if size is not None:
            radar_item.symbol_size = size
        
        # 重新绘制
        radar_item.update()
    
    def set_color(self, data_id: str, color):
        """
        设置颜色
        
        Args:
            data_id: 数据标识符
            color: 颜色，格式为 (R,G,B) 或 '#RRGGBB'
        """
        if not self._initialized or data_id not in self._radar_items:
            return
        
        radar_item = self._radar_items[data_id]
        
        # 更新颜色
        radar_item.color = color
        radar_item.pen = pg.mkPen(color=color, width=radar_item.line_width)
        
        # 更新填充颜色（保持原透明度）
        alpha = radar_item.fill_color[3] if len(radar_item.fill_color) > 3 else 50
        radar_item.fill_color = (*color[:3], alpha)
        radar_item.fill_brush = pg.mkBrush(color=radar_item.fill_color)
        
        # 重新绘制
        radar_item.update()
    
    def set_n_circles(self, n: int):
        """
        设置同心圆数量
        
        Args:
            n: 同心圆数量
        """
        self._n_circles = n
        
        if not self._initialized:
            return
        
        # 更新雷达图轴
        self._update_axis()
    
    def normalize_data(self, data_values: Dict[str, List[float]]):
        """
        将多组数据标准化到相同比例
        
        Args:
            data_values: 数据字典，格式为 {data_id: values}
        """
        if not data_values:
            return
        
        # 找出所有数据的最大值
        all_values = []
        for values in data_values.values():
            all_values.extend(values)
        
        max_value = max(max(all_values), 0.001)  # 避免除以零
        
        # 更新最大值设置
        self.set_max_value(max_value, auto_scale=False)
        
        # 更新所有雷达图项的缩放比例
        for data_id, values in data_values.items():
            if data_id in self._radar_items:
                radar_item = self._radar_items[data_id]
                radar_item.scale = 1.0  # 使用统一比例
                
                # 更新值
                radar_item.values = values
                
                # 重新计算多边形
                radar_item._calculate_polygon()
                
                # 重新绘制
                radar_item.update()
    
    def set_legend_visible(self, visible: bool):
        """
        设置图例是否可见

        Args:
            visible: 是否可见
        """
        self._legend_visible = visible

        if self._use_matplotlib:
            self._implementation.set_legend_visible(visible)
        else:
            if self._initialized and hasattr(self, '_plot_item') and hasattr(self._plot_item, 'legend'):
                if visible:
                    self._plot_item.legend.show()
                else:
                    self._plot_item.legend.hide()

    def clear_data(self, data_id: Optional[str] = None):
        """
        清除图表数据

        Args:
            data_id: 要清除的数据标识符，如果为None则清除所有数据
        """
        try:
            if self._use_matplotlib:
                self._implementation.clear_data(data_id)
            else:
                # 调用父类方法清除数据项
                if hasattr(super(), 'clear_data'):
                    super().clear_data(data_id)

            # 清除雷达图项
            if data_id is None:
                # 清除所有
                self._radar_items = {}
            else:
                # 清除指定ID
                if data_id in self._radar_items:
                    del self._radar_items[data_id]

            logger.debug(f"{self._log_prefix} 清除数据完成: {data_id or '全部'}")

        except Exception as e:
            logger.error(f"{self._log_prefix} 清除数据失败: {str(e)}")

    # 新增方法：提供集成适配器的高级功能
    def set_color(self, data_id: str, color):
        """设置颜色"""
        if self._use_matplotlib:
            self._implementation.set_color(data_id, color)

    def set_n_circles(self, n: int):
        """设置同心圆数量"""
        self._n_circles = n
        if self._use_matplotlib:
            self._implementation.set_n_circles(n)

    def normalize_data(self, data_values: Dict[str, List[float]]):
        """将多组数据标准化到相同比例"""
        if self._use_matplotlib:
            self._implementation.normalize_data(data_values)

    def get_implementation_info(self) -> Dict[str, Any]:
        """获取实现信息"""
        if self._use_matplotlib:
            return self._implementation.get_implementation_info()
        else:
            return {
                'implementation': 'pyqtgraph_fallback',
                'features': {
                    'smart_text_rendering': False,
                    'advanced_styling': False,
                    'theme_support': False,
                    'config_persistence': False,
                    'series_comparison': False
                }
            }
